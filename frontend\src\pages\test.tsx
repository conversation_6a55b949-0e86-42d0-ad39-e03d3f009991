import React from 'react';
import Head from 'next/head';

const TestPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Test Page - DealClosed Partner</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
      </Head>

      <div className="min-vh-100 bg-light">
        <nav className="navbar navbar-expand-lg navbar-dark bg-primary">
          <div className="container">
            <a className="navbar-brand fw-bold" href="/">
              DealClosed Partner
            </a>
          </div>
        </nav>

        <div className="container py-5">
          <div className="row justify-content-center">
            <div className="col-md-8">
              <div className="card shadow-sm border-0">
                <div className="card-body p-5 text-center">
                  <h1 className="display-4 fw-bold text-primary mb-4">
                    🎉 Success!
                  </h1>
                  <h2 className="h3 mb-4">
                    Your DealClosed Partner Platform is Working!
                  </h2>
                  
                  <div className="alert alert-success mb-4">
                    <h5 className="alert-heading">✅ Frontend Setup Complete</h5>
                    <p className="mb-0">
                      Next.js, TypeScript, and Bootstrap are all working correctly.
                    </p>
                  </div>

                  <div className="row g-4 mb-4">
                    <div className="col-md-6">
                      <div className="card bg-light">
                        <div className="card-body">
                          <h6 className="card-title">🚀 Next.js 14</h6>
                          <p className="card-text small">
                            Fast, modern React framework with SSR
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="card bg-light">
                        <div className="card-body">
                          <h6 className="card-title">🎨 Bootstrap 5</h6>
                          <p className="card-text small">
                            Responsive, modern UI framework
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="card bg-light">
                        <div className="card-body">
                          <h6 className="card-title">📝 TypeScript</h6>
                          <p className="card-text small">
                            Type-safe development experience
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="card bg-light">
                        <div className="card-body">
                          <h6 className="card-title">🔐 Authentication</h6>
                          <p className="card-text small">
                            JWT-based auth system ready
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="d-grid gap-2 d-md-flex justify-content-md-center">
                    <a href="/register" className="btn btn-primary btn-lg">
                      Test Registration
                    </a>
                    <a href="/login" className="btn btn-outline-primary btn-lg">
                      Test Login
                    </a>
                  </div>

                  <hr className="my-4" />

                  <div className="text-start">
                    <h5>📋 Next Steps:</h5>
                    <ol className="text-muted">
                      <li>Create PostgreSQL database: <code>CREATE DATABASE sales_platform;</code></li>
                      <li>Start the backend server: <code>cd backend && npm run dev</code></li>
                      <li>Test the complete authentication flow</li>
                      <li>Add more features as needed</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <footer className="bg-dark text-light py-4 mt-5">
          <div className="container text-center">
            <p className="mb-0">&copy; 2025 DealClosed Partner. Platform is ready!</p>
          </div>
        </footer>
      </div>

      <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </>
  );
};

export default TestPage;
