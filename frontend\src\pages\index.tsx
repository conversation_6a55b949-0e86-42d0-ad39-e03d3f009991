import Layout from '@/components/Layout';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';
import React from 'react';
import { FiBriefcase, FiShield, FiTrendingUp, FiUsers } from 'react-icons/fi';

const HomePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-primary text-white py-5">
        <div className="container">
          <div className="row align-items-center min-vh-50">
            <div className="col-lg-6">
              <h1 className="display-4 fw-bold mb-4">
                Connect Companies with Top Sales Professionals
              </h1>
              <p className="lead mb-4">
                DealClosed Partner is the premier platform connecting businesses with skilled freelance sales professionals.
                Find the perfect match for your sales needs or discover exciting opportunities to grow your career.
              </p>
              {!user ? (
                <div className="d-flex gap-3">
                  <Link href="/register" className="btn btn-light btn-lg">
                    Get Started
                  </Link>
                  <Link href="/login" className="btn btn-outline-light btn-lg">
                    Sign In
                  </Link>
                </div>
              ) : (
                <Link href="/dashboard" className="btn btn-light btn-lg">
                  Go to Dashboard
                </Link>
              )}
            </div>
            <div className="col-lg-6 text-center">
              <div className="bg-white bg-opacity-10 rounded-3 p-4">
                <h3 className="mb-3">Join Our Community</h3>
                <div className="row text-center">
                  <div className="col-6">
                    <h4 className="fw-bold">500+</h4>
                    <p className="mb-0">Companies</p>
                  </div>
                  <div className="col-6">
                    <h4 className="fw-bold">1,200+</h4>
                    <p className="mb-0">Sales Professionals</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-5">
        <div className="container">
          <div className="row text-center mb-5">
            <div className="col">
              <h2 className="fw-bold mb-3">Why Choose DealClosed Partner?</h2>
              <p className="lead text-muted">
                We provide the tools and platform you need to succeed in sales partnerships
              </p>
            </div>
          </div>

          <div className="row g-4">
            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 shadow-sm text-center">
                <div className="card-body p-4">
                  <FiUsers size={48} className="text-primary mb-3" />
                  <h5 className="fw-bold">Verified Professionals</h5>
                  <p className="text-muted">
                    All sales professionals are verified with proper credentials and experience validation.
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 shadow-sm text-center">
                <div className="card-body p-4">
                  <FiBriefcase size={48} className="text-primary mb-3" />
                  <h5 className="fw-bold">Quality Opportunities</h5>
                  <p className="text-muted">
                    Access to high-quality sales opportunities from established companies across industries.
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 shadow-sm text-center">
                <div className="card-body p-4">
                  <FiTrendingUp size={48} className="text-primary mb-3" />
                  <h5 className="fw-bold">Performance Tracking</h5>
                  <p className="text-muted">
                    Advanced analytics and reporting to track performance and optimize results.
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 shadow-sm text-center">
                <div className="card-body p-4">
                  <FiShield size={48} className="text-primary mb-3" />
                  <h5 className="fw-bold">Secure Payments</h5>
                  <p className="text-muted">
                    Secure and timely commission payments with full transparency and tracking.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-light py-5">
        <div className="container">
          <div className="row text-center">
            <div className="col-lg-8 mx-auto">
              <h2 className="fw-bold mb-3">Ready to Get Started?</h2>
              <p className="lead text-muted mb-4">
                Join thousands of companies and sales professionals who trust DealClosed Partner
                for their sales partnership needs.
              </p>
              {!user ? (
                <div className="d-flex justify-content-center gap-3">
                  <Link href="/register" className="btn btn-primary btn-lg">
                    Join as Freelancer
                  </Link>
                  <Link href="/register" className="btn btn-outline-primary btn-lg">
                    Post a Job
                  </Link>
                </div>
              ) : (
                <Link href="/profile" className="btn btn-primary btn-lg">
                  Complete Your Profile
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;
