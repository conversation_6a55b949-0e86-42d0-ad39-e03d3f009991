/*! For license information please see react-bootstrap.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["react","react-dom"],t):"object"==typeof exports?exports.ReactBootstrap=t(require("react"),require("react-dom")):e.<PERSON>actBootstrap=t(e.<PERSON><PERSON>,e.ReactDOM)}(self,((e,t)=>(()=>{var n={814:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var a=typeof n;if("string"===a||"number"===a)e.push(n);else if(Array.isArray(n)){if(n.length){var s=o.apply(null,n);s&&e.push(s)}}else if("object"===a){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var i in n)r.call(n,i)&&n[i]&&e.push(i)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},286:e=>{"use strict";e.exports=function(e,t,n,r,o,a,s,i){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,o,a,s,i],u=0;(l=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}}},946:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.default)((function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var o=null;return t.forEach((function(e){if(null==o){var t=e.apply(void 0,n);null!=t&&(o=t)}})),o}))};var r,o=(r=n(844))&&r.__esModule?r:{default:r};e.exports=t.default},844:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(t,n,r,o,a,s){var i=o||"<<anonymous>>",l=s||r;if(null==n[r])return t?new Error("Required "+a+" `"+l+"` was not specified in `"+i+"`."):null;for(var c=arguments.length,u=Array(c>6?c-6:0),d=6;d<c;d++)u[d-6]=arguments[d];return e.apply(void 0,[n,r,i,a,l].concat(u))}var n=t.bind(null,!1);return n.isRequired=t.bind(null,!0),n},e.exports=t.default},428:(e,t,n)=>{"use strict";var r=n(134);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,s){if(s!==r){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},526:(e,t,n)=>{e.exports=n(428)()},134:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},356:(e,t,n)=>{"use strict";var r=n(787),o=60103;if(t.Fragment=60107,"function"==typeof Symbol&&Symbol.for){var a=Symbol.for;o=a("react.element"),t.Fragment=a("react.fragment")}var s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i=Object.prototype.hasOwnProperty,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,a={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:a,_owner:s.current}}t.jsx=c,t.jsxs=c},373:(e,t,n)=>{"use strict";e.exports=n(356)},459:e=>{"use strict";e.exports=function(){}},787:t=>{"use strict";t.exports=e},156:e=>{"use strict";e.exports=t}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var a=r[e]={exports:{}};return n[e](a,a.exports,o),a.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var a={};return(()=>{"use strict";o.r(a),o.d(a,{Accordion:()=>Oe,AccordionBody:()=>be,AccordionButton:()=>ye,AccordionCollapse:()=>pe,AccordionContext:()=>de,AccordionHeader:()=>Ne,AccordionItem:()=>je,Alert:()=>nt,AlertHeading:()=>$e,AlertLink:()=>Ue,Anchor:()=>rt,Badge:()=>at,Breadcrumb:()=>ct,BreadcrumbItem:()=>it,Button:()=>dt,ButtonGroup:()=>pt,ButtonToolbar:()=>vt,Card:()=>Ft,CardBody:()=>bt,CardFooter:()=>gt,CardGroup:()=>Ht,CardHeader:()=>Et,CardImg:()=>Ct,CardImgOverlay:()=>Rt,CardLink:()=>Pt,CardSubtitle:()=>$t,CardText:()=>Mt,CardTitle:()=>Lt,Carousel:()=>Qt,CarouselCaption:()=>qt,CarouselItem:()=>Xt,CloseButton:()=>et,Col:()=>nn,Collapse:()=>le,Container:()=>on,Dropdown:()=>_o,DropdownButton:()=>zo,DropdownDivider:()=>No,DropdownHeader:()=>jo,DropdownItem:()=>Oo,DropdownItemText:()=>ko,DropdownMenu:()=>Ao,DropdownToggle:()=>Bo,Fade:()=>Xe,Figure:()=>ea,FigureCaption:()=>Jo,FigureImage:()=>Yo,FloatingLabel:()=>ka,Form:()=>Sa,FormCheck:()=>ua,FormControl:()=>fa,FormFloating:()=>ma,FormGroup:()=>ha,FormLabel:()=>xa,FormSelect:()=>Na,FormText:()=>ja,Image:()=>Go,InputGroup:()=>Ia,ListGroup:()=>Xa,ListGroupItem:()=>qa,Modal:()=>Ls,ModalBody:()=>ws,ModalDialog:()=>js,ModalFooter:()=>Os,ModalHeader:()=>Ts,ModalTitle:()=>Ds,Nav:()=>Ws,NavDropdown:()=>wi,NavItem:()=>Fs,NavLink:()=>Hs,Navbar:()=>gi,NavbarBrand:()=>zs,NavbarCollapse:()=>qs,NavbarOffcanvas:()=>vi,NavbarText:()=>bi,NavbarToggle:()=>Xs,Offcanvas:()=>pi,OffcanvasBody:()=>ti,OffcanvasHeader:()=>si,OffcanvasTitle:()=>ci,OffcanvasToggling:()=>oi,Overlay:()=>Li,OverlayTrigger:()=>Fi,PageItem:()=>Hi,Pagination:()=>Xi,Placeholder:()=>el,PlaceholderButton:()=>Ji,Popover:()=>Di,PopoverBody:()=>ki,PopoverHeader:()=>Oi,ProgressBar:()=>al,Ratio:()=>sl,Row:()=>ll,SSRProvider:()=>ml,Spinner:()=>ul,SplitButton:()=>pl,Stack:()=>bl,Tab:()=>Il,TabContainer:()=>kl,TabContent:()=>Tl,TabPane:()=>$l,Table:()=>Ll,Tabs:()=>Hl,ThemeProvider:()=>j,Toast:()=>Zl,ToastBody:()=>Xl,ToastContainer:()=>ec,ToastHeader:()=>ql,ToggleButton:()=>rc,ToggleButtonGroup:()=>ac,Tooltip:()=>Ii,useAccordionButton:()=>xe});var e=o(814),t=o.n(e),n=o(787),r=o.n(n);function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s.apply(this,arguments)}function i(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}var l=o(286),c=o.n(l);function u(e){return"default"+e.charAt(0).toUpperCase()+e.substr(1)}function d(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function f(e,t,r){var o=(0,n.useRef)(void 0!==e),a=(0,n.useState)(t),s=a[0],i=a[1],l=void 0!==e,c=o.current;return o.current=l,!l&&c&&s!==t&&i(t),[l?e:s,(0,n.useCallback)((function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];r&&r.apply(void 0,[e].concat(n)),i(e)}),[r])]}function p(e,t){return Object.keys(t).reduce((function(n,r){var o,a=n,l=a[u(r)],c=a[r],p=i(a,[u(r),r].map(d)),m=t[r],v=f(c,l,e[m]),h=v[0],b=v[1];return s({},p,((o={})[r]=h,o[m]=b,o))}),e)}var m=o(373);const v=["xxl","xl","lg","md","sm","xs"],h="xs",b=n.createContext({prefixes:{},breakpoints:v,minBreakpoint:h}),{Consumer:x,Provider:g}=b;function y(e,t){const{prefixes:r}=(0,n.useContext)(b);return e||r[t]||t}function w(){const{breakpoints:e}=(0,n.useContext)(b);return e}function N(){const{minBreakpoint:e}=(0,n.useContext)(b);return e}function E(){const{dir:e}=(0,n.useContext)(b);return"rtl"===e}const j=function({prefixes:e={},breakpoints:t=v,minBreakpoint:r=h,dir:o,children:a}){const s=(0,n.useMemo)((()=>({prefixes:{...e},breakpoints:t,minBreakpoint:r,dir:o})),[e,t,r,o]);return(0,m.jsx)(g,{value:s,children:a})};function C(e){return e&&e.ownerDocument||document}var O=/([A-Z])/g,R=/^ms-/;function k(e){return function(e){return e.replace(O,"-$1").toLowerCase()}(e).replace(R,"-ms-")}var P=/^((translate|rotate|scale)(X|Y|Z|3d)?|matrix(3d)?|perspective|skew(X|Y)?)$/i;const T=function(e,t){var n="",r="";if("string"==typeof t)return e.style.getPropertyValue(k(t))||function(e,t){return function(e){var t=C(e);return t&&t.defaultView||window}(e).getComputedStyle(e,t)}(e).getPropertyValue(k(t));Object.keys(t).forEach((function(o){var a=t[o];a||0===a?function(e){return!(!e||!P.test(e))}(o)?r+=o+"("+a+") ":n+=k(o)+": "+a+";":e.style.removeProperty(k(o))})),r&&(n+="transform: "+r+";"),e.style.cssText+=";"+n};function S(e,t){return S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},S(e,t)}var $=o(156),D=o.n($);const M=r().createContext(null);var I="unmounted",A="exited",L="entering",B="entered",F="exiting",_=function(e){var t,n;function o(t,n){var r;r=e.call(this,t,n)||this;var o,a=n&&!n.isMounting?t.enter:t.appear;return r.appearStatus=null,t.in?a?(o=A,r.appearStatus=L):o=B:o=t.unmountOnExit||t.mountOnEnter?I:A,r.state={status:o},r.nextCallback=null,r}n=e,(t=o).prototype=Object.create(n.prototype),t.prototype.constructor=t,S(t,n),o.getDerivedStateFromProps=function(e,t){return e.in&&t.status===I?{status:A}:null};var a=o.prototype;return a.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},a.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==L&&n!==B&&(t=L):n!==L&&n!==B||(t=F)}this.updateStatus(!1,t)},a.componentWillUnmount=function(){this.cancelNextCallback()},a.getTimeouts=function(){var e,t,n,r=this.props.timeout;return e=t=n=r,null!=r&&"number"!=typeof r&&(e=r.exit,t=r.enter,n=void 0!==r.appear?r.appear:t),{exit:e,enter:t,appear:n}},a.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===L){if(this.props.unmountOnExit||this.props.mountOnEnter){var n=this.props.nodeRef?this.props.nodeRef.current:D().findDOMNode(this);n&&function(e){e.scrollTop}(n)}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===A&&this.setState({status:I})},a.performEnter=function(e){var t=this,n=this.props.enter,r=this.context?this.context.isMounting:e,o=this.props.nodeRef?[r]:[D().findDOMNode(this),r],a=o[0],s=o[1],i=this.getTimeouts(),l=r?i.appear:i.enter;e||n?(this.props.onEnter(a,s),this.safeSetState({status:L},(function(){t.props.onEntering(a,s),t.onTransitionEnd(l,(function(){t.safeSetState({status:B},(function(){t.props.onEntered(a,s)}))}))}))):this.safeSetState({status:B},(function(){t.props.onEntered(a)}))},a.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts(),r=this.props.nodeRef?void 0:D().findDOMNode(this);t?(this.props.onExit(r),this.safeSetState({status:F},(function(){e.props.onExiting(r),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:A},(function(){e.props.onExited(r)}))}))}))):this.safeSetState({status:A},(function(){e.props.onExited(r)}))},a.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},a.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},a.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(r){n&&(n=!1,t.nextCallback=null,e(r))},this.nextCallback.cancel=function(){n=!1},this.nextCallback},a.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=this.props.nodeRef?this.props.nodeRef.current:D().findDOMNode(this),r=null==e&&!this.props.addEndListener;if(n&&!r){if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[n,this.nextCallback],a=o[0],s=o[1];this.props.addEndListener(a,s)}null!=e&&setTimeout(this.nextCallback,e)}else setTimeout(this.nextCallback,0)},a.render=function(){var e=this.state.status;if(e===I)return null;var t=this.props,n=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,i(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return r().createElement(M.Provider,{value:null},"function"==typeof n?n(e,o):r().cloneElement(r().Children.only(n),o))},o}(r().Component);function H(){}_.contextType=M,_.propTypes={},_.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:H,onEntering:H,onEntered:H,onExit:H,onExiting:H,onExited:H},_.UNMOUNTED=I,_.EXITED=A,_.ENTERING=L,_.ENTERED=B,_.EXITING=F;const K=_,W=!("undefined"==typeof window||!window.document||!window.document.createElement);var V=!1,z=!1;try{var U={get passive(){return V=!0},get once(){return z=V=!0}};W&&(window.addEventListener("test",U,U),window.removeEventListener("test",U,!0))}catch(e){}const q=function(e,t,n,r){if(r&&"boolean"!=typeof r&&!z){var o=r.once,a=r.capture,s=n;!z&&o&&(s=n.__once||function e(r){this.removeEventListener(t,e,a),n.call(this,r)},n.__once=s),e.addEventListener(t,s,V?r:a)}e.addEventListener(t,n,r)},G=function(e,t,n,r){var o=r&&"boolean"!=typeof r?r.capture:r;e.removeEventListener(t,n,o),n.__once&&e.removeEventListener(t,n.__once,o)},X=function(e,t,n,r){return q(e,t,n,r),function(){G(e,t,n,r)}};function Y(e,t,n,r){var o,a;null==n&&(a=-1===(o=T(e,"transitionDuration")||"").indexOf("ms")?1e3:1,n=parseFloat(o)*a||0);var s=function(e,t,n){void 0===n&&(n=5);var r=!1,o=setTimeout((function(){r||function(e,t,n,r){if(void 0===n&&(n=!1),void 0===r&&(r=!0),e){var o=document.createEvent("HTMLEvents");o.initEvent("transitionend",n,r),e.dispatchEvent(o)}}(e,0,!0)}),t+n),a=X(e,"transitionend",(function(){r=!0}),{once:!0});return function(){clearTimeout(o),a()}}(e,n,r),i=X(e,"transitionend",t);return function(){s(),i()}}function Z(e,t){const n=T(e,t)||"",r=-1===n.indexOf("ms")?1e3:1;return parseFloat(n)*r}function J(e,t){const n=Z(e,"transitionDuration"),r=Z(e,"transitionDelay"),o=Y(e,(n=>{n.target===e&&(o(),t(n))}),n+r)}const Q=function(...e){return e.filter((e=>null!=e)).reduce(((e,t)=>{if("function"!=typeof t)throw new Error("Invalid Argument Type, must only provide functions, undefined, or null.");return null===e?t:function(...n){e.apply(this,n),t.apply(this,n)}}),null)};function ee(e){e.offsetHeight}var te=function(e){return e&&"function"!=typeof e?function(t){e.current=t}:e};const ne=function(e,t){return(0,n.useMemo)((function(){return function(e,t){var n=te(e),r=te(t);return function(e){n&&n(e),r&&r(e)}}(e,t)}),[e,t])};function re(e){return e&&"setState"in e?D().findDOMNode(e):null!=e?e:null}const oe=r().forwardRef((({onEnter:e,onEntering:t,onEntered:o,onExit:a,onExiting:s,onExited:i,addEndListener:l,children:c,childRef:u,...d},f)=>{const p=(0,n.useRef)(null),v=ne(p,u),h=e=>{v(re(e))},b=e=>t=>{e&&p.current&&e(p.current,t)},x=(0,n.useCallback)(b(e),[e]),g=(0,n.useCallback)(b(t),[t]),y=(0,n.useCallback)(b(o),[o]),w=(0,n.useCallback)(b(a),[a]),N=(0,n.useCallback)(b(s),[s]),E=(0,n.useCallback)(b(i),[i]),j=(0,n.useCallback)(b(l),[l]);return(0,m.jsx)(K,{ref:f,...d,onEnter:x,onEntered:y,onEntering:g,onExit:w,onExited:E,onExiting:N,addEndListener:j,nodeRef:p,children:"function"==typeof c?(e,t)=>c(e,{...t,ref:h}):r().cloneElement(c,{ref:h})})})),ae={height:["marginTop","marginBottom"],width:["marginLeft","marginRight"]};function se(e,t){const n=t[`offset${e[0].toUpperCase()}${e.slice(1)}`],r=ae[e];return n+parseInt(T(t,r[0]),10)+parseInt(T(t,r[1]),10)}const ie={[A]:"collapse",[F]:"collapsing",[L]:"collapsing",[B]:"collapse show"},le=r().forwardRef((({onEnter:e,onEntering:o,onEntered:a,onExit:s,onExiting:i,className:l,children:c,dimension:u="height",in:d=!1,timeout:f=300,mountOnEnter:p=!1,unmountOnExit:v=!1,appear:h=!1,getDimensionValue:b=se,...x},g)=>{const y="function"==typeof u?u():u,w=(0,n.useMemo)((()=>Q((e=>{e.style[y]="0"}),e)),[y,e]),N=(0,n.useMemo)((()=>Q((e=>{const t=`scroll${y[0].toUpperCase()}${y.slice(1)}`;e.style[y]=`${e[t]}px`}),o)),[y,o]),E=(0,n.useMemo)((()=>Q((e=>{e.style[y]=null}),a)),[y,a]),j=(0,n.useMemo)((()=>Q((e=>{e.style[y]=`${b(y,e)}px`,ee(e)}),s)),[s,b,y]),C=(0,n.useMemo)((()=>Q((e=>{e.style[y]=null}),i)),[y,i]);return(0,m.jsx)(oe,{ref:g,addEndListener:J,...x,"aria-expanded":x.role?d:null,onEnter:w,onEntering:N,onEntered:E,onExit:j,onExiting:C,childRef:c.ref,in:d,timeout:f,mountOnEnter:p,unmountOnExit:v,appear:h,children:(e,n)=>r().cloneElement(c,{...n,className:t()(l,c.props.className,ie[e],"width"===y&&"collapse-horizontal")})})}));function ce(e,t){return Array.isArray(e)?e.includes(t):e===t}const ue=n.createContext({});ue.displayName="AccordionContext";const de=ue,fe=n.forwardRef((({as:e="div",bsPrefix:r,className:o,children:a,eventKey:s,...i},l)=>{const{activeEventKey:c}=(0,n.useContext)(de);return r=y(r,"accordion-collapse"),(0,m.jsx)(le,{ref:l,in:ce(c,s),...i,className:t()(o,r),children:(0,m.jsx)(e,{children:n.Children.only(a)})})}));fe.displayName="AccordionCollapse";const pe=fe,me=n.createContext({eventKey:""});me.displayName="AccordionItemContext";const ve=me,he=n.forwardRef((({as:e="div",bsPrefix:r,className:o,onEnter:a,onEntering:s,onEntered:i,onExit:l,onExiting:c,onExited:u,...d},f)=>{r=y(r,"accordion-body");const{eventKey:p}=(0,n.useContext)(ve);return(0,m.jsx)(pe,{eventKey:p,onEnter:a,onEntering:s,onEntered:i,onExit:l,onExiting:c,onExited:u,children:(0,m.jsx)(e,{ref:f,...d,className:t()(o,r)})})}));he.displayName="AccordionBody";const be=he;function xe(e,t){const{activeEventKey:r,onSelect:o,alwaysOpen:a}=(0,n.useContext)(de);return n=>{let s=e===r?null:e;a&&(s=Array.isArray(r)?r.includes(e)?r.filter((t=>t!==e)):[...r,e]:[e]),null==o||o(s,n),null==t||t(n)}}const ge=n.forwardRef((({as:e="button",bsPrefix:r,className:o,onClick:a,...s},i)=>{r=y(r,"accordion-button");const{eventKey:l}=(0,n.useContext)(ve),c=xe(l,a),{activeEventKey:u}=(0,n.useContext)(de);return"button"===e&&(s.type="button"),(0,m.jsx)(e,{ref:i,onClick:c,...s,"aria-expanded":Array.isArray(u)?u.includes(l):l===u,className:t()(o,r,!ce(u,l)&&"collapsed")})}));ge.displayName="AccordionButton";const ye=ge,we=n.forwardRef((({as:e="h2",bsPrefix:n,className:r,children:o,onClick:a,...s},i)=>(n=y(n,"accordion-header"),(0,m.jsx)(e,{ref:i,...s,className:t()(r,n),children:(0,m.jsx)(ye,{onClick:a,children:o})}))));we.displayName="AccordionHeader";const Ne=we,Ee=n.forwardRef((({as:e="div",bsPrefix:r,className:o,eventKey:a,...s},i)=>{r=y(r,"accordion-item");const l=(0,n.useMemo)((()=>({eventKey:a})),[a]);return(0,m.jsx)(ve.Provider,{value:l,children:(0,m.jsx)(e,{ref:i,...s,className:t()(o,r)})})}));Ee.displayName="AccordionItem";const je=Ee,Ce=n.forwardRef(((e,r)=>{const{as:o="div",activeKey:a,bsPrefix:s,className:i,onSelect:l,flush:c,alwaysOpen:u,...d}=p(e,{activeKey:"onSelect"}),f=y(s,"accordion"),v=(0,n.useMemo)((()=>({activeEventKey:a,onSelect:l,alwaysOpen:u})),[a,l,u]);return(0,m.jsx)(de.Provider,{value:v,children:(0,m.jsx)(o,{ref:r,...d,className:t()(i,f,c&&`${f}-flush`)})})}));Ce.displayName="Accordion";const Oe=Object.assign(Ce,{Button:ye,Collapse:pe,Item:je,Header:Ne,Body:be}),Re=function(e){var t=(0,n.useRef)(e);return(0,n.useEffect)((function(){t.current=e}),[e]),t};function ke(e){var t=Re(e);return(0,n.useCallback)((function(){return t.current&&t.current.apply(t,arguments)}),[t])}const Pe=e=>n.forwardRef(((n,r)=>(0,m.jsx)("div",{...n,ref:r,className:t()(n.className,e)}))),Te=Pe("h4");Te.displayName="DivStyledAsH4";const Se=n.forwardRef((({className:e,bsPrefix:n,as:r=Te,...o},a)=>(n=y(n,"alert-heading"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Se.displayName="AlertHeading";const $e=Se;function De(){return(0,n.useState)(null)}function Me(){var e=(0,n.useRef)(!0),t=(0,n.useRef)((function(){return e.current}));return(0,n.useEffect)((function(){return e.current=!0,function(){e.current=!1}}),[]),t.current}function Ie(e){var t=(0,n.useRef)(null);return(0,n.useEffect)((function(){t.current=e})),t.current}var Ae=void 0!==o.g&&o.g.navigator&&"ReactNative"===o.g.navigator.product;const Le="undefined"!=typeof document||Ae?n.useLayoutEffect:n.useEffect;new WeakMap;const Be=["as","disabled"];function Fe({tagName:e,disabled:t,href:n,target:r,rel:o,role:a,onClick:s,tabIndex:i=0,type:l}){e||(e=null!=n||null!=r||null!=o?"a":"button");const c={tagName:e};if("button"===e)return[{type:l||"button",disabled:t},c];const u=r=>{(t||"a"===e&&function(e){return!e||"#"===e.trim()}(n))&&r.preventDefault(),t?r.stopPropagation():null==s||s(r)};return"a"===e&&(n||(n="#"),t&&(n=void 0)),[{role:null!=a?a:"button",disabled:void 0,tabIndex:t?void 0:i,href:n,target:"a"===e?r:void 0,"aria-disabled":t||void 0,rel:"a"===e?o:void 0,onClick:u,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),u(e))}},c]}const _e=n.forwardRef(((e,t)=>{let{as:n,disabled:r}=e,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,Be);const[a,{tagName:s}]=Fe(Object.assign({tagName:n,disabled:r},o));return(0,m.jsx)(s,Object.assign({},o,a,{ref:t}))}));_e.displayName="Button";const He=_e,Ke=["onKeyDown"],We=n.forwardRef(((e,t)=>{let{onKeyDown:n}=e,r=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,Ke);const[o]=Fe(Object.assign({tagName:"a"},r)),a=ke((e=>{o.onKeyDown(e),null==n||n(e)}));return(s=r.href)&&"#"!==s.trim()&&"button"!==r.role?(0,m.jsx)("a",Object.assign({ref:t},r,{onKeyDown:n})):(0,m.jsx)("a",Object.assign({ref:t},r,o,{onKeyDown:a}));var s}));We.displayName="Anchor";const Ve=We,ze=n.forwardRef((({className:e,bsPrefix:n,as:r=Ve,...o},a)=>(n=y(n,"alert-link"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));ze.displayName="AlertLink";const Ue=ze,qe={[L]:"show",[B]:"show"},Ge=n.forwardRef((({className:e,children:r,transitionClasses:o={},onEnter:a,...s},i)=>{const l={in:!1,timeout:300,mountOnEnter:!1,unmountOnExit:!1,appear:!1,...s},c=(0,n.useCallback)(((e,t)=>{ee(e),null==a||a(e,t)}),[a]);return(0,m.jsx)(oe,{ref:i,addEndListener:J,...l,onEnter:c,childRef:r.ref,children:(a,s)=>n.cloneElement(r,{...s,className:t()("fade",e,r.props.className,qe[a],o[a])})})}));Ge.displayName="Fade";const Xe=Ge;var Ye=o(526),Ze=o.n(Ye);const Je={"aria-label":Ze().string,onClick:Ze().func,variant:Ze().oneOf(["white"])},Qe=n.forwardRef((({className:e,variant:n,"aria-label":r="Close",...o},a)=>(0,m.jsx)("button",{ref:a,type:"button",className:t()("btn-close",n&&`btn-close-${n}`,e),"aria-label":r,...o})));Qe.displayName="CloseButton",Qe.propTypes=Je;const et=Qe,tt=n.forwardRef(((e,n)=>{const{bsPrefix:r,show:o=!0,closeLabel:a="Close alert",closeVariant:s,className:i,children:l,variant:c="primary",onClose:u,dismissible:d,transition:f=Xe,...v}=p(e,{show:"onClose"}),h=y(r,"alert"),b=ke((e=>{u&&u(!1,e)})),x=!0===f?Xe:f,g=(0,m.jsxs)("div",{role:"alert",...x?void 0:v,ref:n,className:t()(i,h,c&&`${h}-${c}`,d&&`${h}-dismissible`),children:[d&&(0,m.jsx)(et,{onClick:b,"aria-label":a,variant:s}),l]});return x?(0,m.jsx)(x,{unmountOnExit:!0,...v,ref:void 0,in:o,children:g}):o?g:null}));tt.displayName="Alert";const nt=Object.assign(tt,{Link:Ue,Heading:$e}),rt=Ve,ot=n.forwardRef((({bsPrefix:e,bg:n="primary",pill:r=!1,text:o,className:a,as:s="span",...i},l)=>{const c=y(e,"badge");return(0,m.jsx)(s,{ref:l,...i,className:t()(a,c,r&&"rounded-pill",o&&`text-${o}`,n&&`bg-${n}`)})}));ot.displayName="Badge";const at=ot,st=n.forwardRef((({bsPrefix:e,active:n=!1,children:r,className:o,as:a="li",linkAs:s=Ve,linkProps:i={},href:l,title:c,target:u,...d},f)=>{const p=y(e,"breadcrumb-item");return(0,m.jsx)(a,{ref:f,...d,className:t()(p,o,{active:n}),"aria-current":n?"page":void 0,children:n?r:(0,m.jsx)(s,{...i,href:l,title:c,target:u,children:r})})}));st.displayName="BreadcrumbItem";const it=st,lt=n.forwardRef((({bsPrefix:e,className:n,listProps:r={},children:o,label:a="breadcrumb",as:s="nav",...i},l)=>{const c=y(e,"breadcrumb");return(0,m.jsx)(s,{"aria-label":a,className:n,ref:l,...i,children:(0,m.jsx)("ol",{...r,className:t()(c,null==r?void 0:r.className),children:o})})}));lt.displayName="Breadcrumb";const ct=Object.assign(lt,{Item:it}),ut=n.forwardRef((({as:e,bsPrefix:n,variant:r="primary",size:o,active:a=!1,disabled:s=!1,className:i,...l},c)=>{const u=y(n,"btn"),[d,{tagName:f}]=Fe({tagName:e,disabled:s,...l}),p=f;return(0,m.jsx)(p,{...d,...l,ref:c,disabled:s,className:t()(i,u,a&&"active",r&&`${u}-${r}`,o&&`${u}-${o}`,l.href&&s&&"disabled")})}));ut.displayName="Button";const dt=ut,ft=n.forwardRef((({bsPrefix:e,size:n,vertical:r=!1,className:o,role:a="group",as:s="div",...i},l)=>{const c=y(e,"btn-group");let u=c;return r&&(u=`${c}-vertical`),(0,m.jsx)(s,{...i,ref:l,role:a,className:t()(o,u,n&&`${c}-${n}`)})}));ft.displayName="ButtonGroup";const pt=ft,mt=n.forwardRef((({bsPrefix:e,className:n,role:r="toolbar",...o},a)=>{const s=y(e,"btn-toolbar");return(0,m.jsx)("div",{...o,ref:a,className:t()(n,s),role:r})}));mt.displayName="ButtonToolbar";const vt=mt,ht=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"card-body"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));ht.displayName="CardBody";const bt=ht,xt=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"card-footer"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));xt.displayName="CardFooter";const gt=xt,yt=n.createContext(null);yt.displayName="CardHeaderContext";const wt=yt,Nt=n.forwardRef((({bsPrefix:e,className:r,as:o="div",...a},s)=>{const i=y(e,"card-header"),l=(0,n.useMemo)((()=>({cardHeaderBsPrefix:i})),[i]);return(0,m.jsx)(wt.Provider,{value:l,children:(0,m.jsx)(o,{ref:s,...a,className:t()(r,i)})})}));Nt.displayName="CardHeader";const Et=Nt,jt=n.forwardRef((({bsPrefix:e,className:n,variant:r,as:o="img",...a},s)=>{const i=y(e,"card-img");return(0,m.jsx)(o,{ref:s,className:t()(r?`${i}-${r}`:i,n),...a})}));jt.displayName="CardImg";const Ct=jt,Ot=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"card-img-overlay"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ot.displayName="CardImgOverlay";const Rt=Ot,kt=n.forwardRef((({className:e,bsPrefix:n,as:r="a",...o},a)=>(n=y(n,"card-link"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));kt.displayName="CardLink";const Pt=kt,Tt=Pe("h6"),St=n.forwardRef((({className:e,bsPrefix:n,as:r=Tt,...o},a)=>(n=y(n,"card-subtitle"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));St.displayName="CardSubtitle";const $t=St,Dt=n.forwardRef((({className:e,bsPrefix:n,as:r="p",...o},a)=>(n=y(n,"card-text"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Dt.displayName="CardText";const Mt=Dt,It=Pe("h5"),At=n.forwardRef((({className:e,bsPrefix:n,as:r=It,...o},a)=>(n=y(n,"card-title"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));At.displayName="CardTitle";const Lt=At,Bt=n.forwardRef((({bsPrefix:e,className:n,bg:r,text:o,border:a,body:s=!1,children:i,as:l="div",...c},u)=>{const d=y(e,"card");return(0,m.jsx)(l,{ref:u,...c,className:t()(n,d,r&&`bg-${r}`,o&&`text-${o}`,a&&`border-${a}`),children:s?(0,m.jsx)(bt,{children:i}):i})}));Bt.displayName="Card";const Ft=Object.assign(Bt,{Img:Ct,Title:Lt,Subtitle:$t,Body:bt,Link:Pt,Text:Mt,Header:Et,Footer:gt,ImgOverlay:Rt}),_t=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"card-group"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));_t.displayName="CardGroup";const Ht=_t;function Kt(e){var t,r,o=(t=e,(r=(0,n.useRef)(t)).current=t,r);(0,n.useEffect)((function(){return function(){return o.current()}}),[])}var Wt=Math.pow(2,31)-1;function Vt(e,t,n){var r=n-Date.now();e.current=r<=Wt?setTimeout(t,r):setTimeout((function(){return Vt(e,t,n)}),Wt)}function zt(){var e=Me(),t=(0,n.useRef)();return Kt((function(){return clearTimeout(t.current)})),(0,n.useMemo)((function(){var n=function(){return clearTimeout(t.current)};return{set:function(r,o){void 0===o&&(o=0),e()&&(n(),o<=Wt?t.current=setTimeout(r,o):Vt(t,r,Date.now()+o))},clear:n}}),[])}const Ut=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"carousel-caption"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ut.displayName="CarouselCaption";const qt=Ut,Gt=n.forwardRef((({as:e="div",bsPrefix:n,className:r,...o},a)=>{const s=t()(r,y(n,"carousel-item"));return(0,m.jsx)(e,{ref:a,...o,className:s})}));Gt.displayName="CarouselItem";const Xt=Gt;function Yt(e,t){let r=0;return n.Children.map(e,(e=>n.isValidElement(e)?t(e,r++):e))}function Zt(e,t){let r=0;n.Children.forEach(e,(e=>{n.isValidElement(e)&&t(e,r++)}))}const Jt=n.forwardRef((({defaultActiveIndex:e=0,...r},o)=>{const{as:a="div",bsPrefix:s,slide:i=!0,fade:l=!1,controls:c=!0,indicators:u=!0,indicatorLabels:d=[],activeIndex:f,onSelect:v,onSlide:h,onSlid:b,interval:x=5e3,keyboard:g=!0,onKeyDown:w,pause:N="hover",onMouseOver:j,onMouseOut:C,wrap:O=!0,touch:R=!0,onTouchStart:k,onTouchMove:P,onTouchEnd:T,prevIcon:S=(0,m.jsx)("span",{"aria-hidden":"true",className:"carousel-control-prev-icon"}),prevLabel:$="Previous",nextIcon:D=(0,m.jsx)("span",{"aria-hidden":"true",className:"carousel-control-next-icon"}),nextLabel:M="Next",variant:I,className:A,children:L,...B}=p({defaultActiveIndex:e,...r},{activeIndex:"onSelect"}),F=y(s,"carousel"),_=E(),H=(0,n.useRef)(null),[K,W]=(0,n.useState)("next"),[V,z]=(0,n.useState)(!1),[U,q]=(0,n.useState)(!1),[G,X]=(0,n.useState)(f||0);(0,n.useEffect)((()=>{U||f===G||(H.current?W(H.current):W((f||0)>G?"next":"prev"),i&&q(!0),X(f||0))}),[f,U,G,i]),(0,n.useEffect)((()=>{H.current&&(H.current=null)}));let Y,Z=0;Zt(L,((e,t)=>{++Z,t===f&&(Y=e.props.interval)}));const Q=Re(Y),te=(0,n.useCallback)((e=>{if(U)return;let t=G-1;if(t<0){if(!O)return;t=Z-1}H.current="prev",null==v||v(t,e)}),[U,G,v,O,Z]),ne=ke((e=>{if(U)return;let t=G+1;if(t>=Z){if(!O)return;t=0}H.current="next",null==v||v(t,e)})),re=(0,n.useRef)();(0,n.useImperativeHandle)(o,(()=>({element:re.current,prev:te,next:ne})));const ae=ke((()=>{!document.hidden&&function(e){if(!(e&&e.style&&e.parentNode&&e.parentNode.style))return!1;const t=getComputedStyle(e);return"none"!==t.display&&"hidden"!==t.visibility&&"none"!==getComputedStyle(e.parentNode).display}(re.current)&&(_?te():ne())})),se="next"===K?"start":"end";var ie,le,ce;ie=()=>{i||(null==h||h(G,se),null==b||b(G,se))},le=[G],ce=(0,n.useRef)(!0),(0,n.useEffect)((function(){if(!ce.current)return ie();ce.current=!1}),le);const ue=`${F}-item-${K}`,de=`${F}-item-${se}`,fe=(0,n.useCallback)((e=>{ee(e),null==h||h(G,se)}),[h,G,se]),pe=(0,n.useCallback)((()=>{q(!1),null==b||b(G,se)}),[b,G,se]),me=(0,n.useCallback)((e=>{if(g&&!/input|textarea/i.test(e.target.tagName))switch(e.key){case"ArrowLeft":return e.preventDefault(),void(_?ne(e):te(e));case"ArrowRight":return e.preventDefault(),void(_?te(e):ne(e))}null==w||w(e)}),[g,w,te,ne,_]),ve=(0,n.useCallback)((e=>{"hover"===N&&z(!0),null==j||j(e)}),[N,j]),he=(0,n.useCallback)((e=>{z(!1),null==C||C(e)}),[C]),be=(0,n.useRef)(0),xe=(0,n.useRef)(0),ge=zt(),ye=(0,n.useCallback)((e=>{be.current=e.touches[0].clientX,xe.current=0,"hover"===N&&z(!0),null==k||k(e)}),[N,k]),we=(0,n.useCallback)((e=>{e.touches&&e.touches.length>1?xe.current=0:xe.current=e.touches[0].clientX-be.current,null==P||P(e)}),[P]),Ne=(0,n.useCallback)((e=>{if(R){const t=xe.current;Math.abs(t)>40&&(t>0?te(e):ne(e))}"hover"===N&&ge.set((()=>{z(!1)}),x||void 0),null==T||T(e)}),[R,N,te,ne,ge,x,T]),Ee=null!=x&&!V&&!U,je=(0,n.useRef)();(0,n.useEffect)((()=>{var e,t;if(!Ee)return;const n=_?te:ne;return je.current=window.setInterval(document.visibilityState?ae:n,null!=(e=null!=(t=Q.current)?t:x)?e:void 0),()=>{null!==je.current&&clearInterval(je.current)}}),[Ee,te,ne,Q,x,ae,_]);const Ce=(0,n.useMemo)((()=>u&&Array.from({length:Z},((e,t)=>e=>{null==v||v(t,e)}))),[u,Z,v]);return(0,m.jsxs)(a,{ref:re,...B,onKeyDown:me,onMouseOver:ve,onMouseOut:he,onTouchStart:ye,onTouchMove:we,onTouchEnd:Ne,className:t()(A,F,i&&"slide",l&&`${F}-fade`,I&&`${F}-${I}`),children:[u&&(0,m.jsx)("div",{className:`${F}-indicators`,children:Yt(L,((e,t)=>(0,m.jsx)("button",{type:"button","data-bs-target":"","aria-label":null!=d&&d.length?d[t]:`Slide ${t+1}`,className:t===G?"active":void 0,onClick:Ce?Ce[t]:void 0,"aria-current":t===G},t)))}),(0,m.jsx)("div",{className:`${F}-inner`,children:Yt(L,((e,r)=>{const o=r===G;return i?(0,m.jsx)(oe,{in:o,onEnter:o?fe:void 0,onEntered:o?pe:void 0,addEndListener:J,children:(r,a)=>n.cloneElement(e,{...a,className:t()(e.props.className,o&&"entered"!==r&&ue,("entered"===r||"exiting"===r)&&"active",("entering"===r||"exiting"===r)&&de)})}):n.cloneElement(e,{className:t()(e.props.className,o&&"active")})}))}),c&&(0,m.jsxs)(m.Fragment,{children:[(O||0!==f)&&(0,m.jsxs)(Ve,{className:`${F}-control-prev`,onClick:te,children:[S,$&&(0,m.jsx)("span",{className:"visually-hidden",children:$})]}),(O||f!==Z-1)&&(0,m.jsxs)(Ve,{className:`${F}-control-next`,onClick:ne,children:[D,M&&(0,m.jsx)("span",{className:"visually-hidden",children:M})]})]})]})}));Jt.displayName="Carousel";const Qt=Object.assign(Jt,{Caption:qt,Item:Xt});function en({as:e,bsPrefix:n,className:r,...o}){n=y(n,"col");const a=w(),s=N(),i=[],l=[];return a.forEach((e=>{const t=o[e];let r,a,c;delete o[e],"object"==typeof t&&null!=t?({span:r,offset:a,order:c}=t):r=t;const u=e!==s?`-${e}`:"";r&&i.push(!0===r?`${n}${u}`:`${n}${u}-${r}`),null!=c&&l.push(`order${u}-${c}`),null!=a&&l.push(`offset${u}-${a}`)})),[{...o,className:t()(r,...i,...l)},{as:e,bsPrefix:n,spans:i}]}const tn=n.forwardRef(((e,n)=>{const[{className:r,...o},{as:a="div",bsPrefix:s,spans:i}]=en(e);return(0,m.jsx)(a,{...o,ref:n,className:t()(r,!i.length&&s)})}));tn.displayName="Col";const nn=tn,rn=n.forwardRef((({bsPrefix:e,fluid:n=!1,as:r="div",className:o,...a},s)=>{const i=y(e,"container"),l="string"==typeof n?`-${n}`:"-fluid";return(0,m.jsx)(r,{ref:s,...a,className:t()(o,n?`${i}${l}`:i)})}));rn.displayName="Container";const on=rn;var an=Function.prototype.bind.call(Function.prototype.call,[].slice);function sn(e,t){return an(e.querySelectorAll(t))}function ln(e,t,r){const o=(0,n.useRef)(void 0!==e),[a,s]=(0,n.useState)(t),i=void 0!==e,l=o.current;return o.current=i,!i&&l&&a!==t&&s(t),[i?e:a,(0,n.useCallback)(((e,...t)=>{r&&r(e,...t),s(e)}),[r])]}function cn(){return(0,n.useReducer)((function(e){return!e}),!1)[1]}const un=n.createContext(null);var dn=Object.prototype.hasOwnProperty;function fn(e,t,n){for(n of e.keys())if(pn(n,t))return n}function pn(e,t){var n,r,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&pn(e[r],t[r]););return-1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e){if((o=r)&&"object"==typeof o&&!(o=fn(t,o)))return!1;if(!t.has(o))return!1}return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e){if((o=r[0])&&"object"==typeof o&&!(o=fn(t,o)))return!1;if(!pn(r[1],t.get(o)))return!1}return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return-1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return-1===r}if(!n||"object"==typeof e){for(n in r=0,e){if(dn.call(e,n)&&++r&&!dn.call(t,n))return!1;if(!(n in t)||!pn(e[n],t[n]))return!1}return Object.keys(t).length===r}}return e!=e&&t!=t}function mn(e){return e.split("-")[0]}function vn(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function hn(e){return e instanceof vn(e).Element||e instanceof Element}function bn(e){return e instanceof vn(e).HTMLElement||e instanceof HTMLElement}function xn(e){return"undefined"!=typeof ShadowRoot&&(e instanceof vn(e).ShadowRoot||e instanceof ShadowRoot)}var gn=Math.max,yn=Math.min,wn=Math.round;function Nn(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function En(){return!/^((?!chrome|android).)*safari/i.test(Nn())}function jn(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),o=1,a=1;t&&bn(e)&&(o=e.offsetWidth>0&&wn(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&wn(r.height)/e.offsetHeight||1);var s=(hn(e)?vn(e):window).visualViewport,i=!En()&&n,l=(r.left+(i&&s?s.offsetLeft:0))/o,c=(r.top+(i&&s?s.offsetTop:0))/a,u=r.width/o,d=r.height/a;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function Cn(e){var t=jn(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function On(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&xn(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Rn(e){return e?(e.nodeName||"").toLowerCase():null}function kn(e){return vn(e).getComputedStyle(e)}function Pn(e){return["table","td","th"].indexOf(Rn(e))>=0}function Tn(e){return((hn(e)?e.ownerDocument:e.document)||window.document).documentElement}function Sn(e){return"html"===Rn(e)?e:e.assignedSlot||e.parentNode||(xn(e)?e.host:null)||Tn(e)}function $n(e){return bn(e)&&"fixed"!==kn(e).position?e.offsetParent:null}function Dn(e){for(var t=vn(e),n=$n(e);n&&Pn(n)&&"static"===kn(n).position;)n=$n(n);return n&&("html"===Rn(n)||"body"===Rn(n)&&"static"===kn(n).position)?t:n||function(e){var t=/firefox/i.test(Nn());if(/Trident/i.test(Nn())&&bn(e)&&"fixed"===kn(e).position)return null;var n=Sn(e);for(xn(n)&&(n=n.host);bn(n)&&["html","body"].indexOf(Rn(n))<0;){var r=kn(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function Mn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function In(e,t,n){return gn(e,yn(t,n))}function An(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ln(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var Bn="top",Fn="bottom",_n="right",Hn="left",Kn="auto",Wn=[Bn,Fn,_n,Hn],Vn="start",zn="end",Un="viewport",qn="popper",Gn=Wn.reduce((function(e,t){return e.concat([t+"-"+Vn,t+"-"+zn])}),[]),Xn=[].concat(Wn,[Kn]).reduce((function(e,t){return e.concat([t,t+"-"+Vn,t+"-"+zn])}),[]),Yn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];const Zn={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,a=n.elements.arrow,s=n.modifiersData.popperOffsets,i=mn(n.placement),l=Mn(i),c=[Hn,_n].indexOf(i)>=0?"height":"width";if(a&&s){var u=function(e,t){return An("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Ln(e,Wn))}(o.padding,n),d=Cn(a),f="y"===l?Bn:Hn,p="y"===l?Fn:_n,m=n.rects.reference[c]+n.rects.reference[l]-s[l]-n.rects.popper[c],v=s[l]-n.rects.reference[l],h=Dn(a),b=h?"y"===l?h.clientHeight||0:h.clientWidth||0:0,x=m/2-v/2,g=u[f],y=b-d[c]-u[p],w=b/2-d[c]/2+x,N=In(g,w,y),E=l;n.modifiersData[r]=((t={})[E]=N,t.centerOffset=N-w,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&On(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Jn(e){return e.split("-")[1]}var Qn={top:"auto",right:"auto",bottom:"auto",left:"auto"};function er(e){var t,n=e.popper,r=e.popperRect,o=e.placement,a=e.variation,s=e.offsets,i=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,f=s.x,p=void 0===f?0:f,m=s.y,v=void 0===m?0:m,h="function"==typeof u?u({x:p,y:v}):{x:p,y:v};p=h.x,v=h.y;var b=s.hasOwnProperty("x"),x=s.hasOwnProperty("y"),g=Hn,y=Bn,w=window;if(c){var N=Dn(n),E="clientHeight",j="clientWidth";N===vn(n)&&"static"!==kn(N=Tn(n)).position&&"absolute"===i&&(E="scrollHeight",j="scrollWidth"),(o===Bn||(o===Hn||o===_n)&&a===zn)&&(y=Fn,v-=(d&&N===w&&w.visualViewport?w.visualViewport.height:N[E])-r.height,v*=l?1:-1),o!==Hn&&(o!==Bn&&o!==Fn||a!==zn)||(g=_n,p-=(d&&N===w&&w.visualViewport?w.visualViewport.width:N[j])-r.width,p*=l?1:-1)}var C,O=Object.assign({position:i},c&&Qn),R=!0===u?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:wn(n*o)/o||0,y:wn(r*o)/o||0}}({x:p,y:v},vn(n)):{x:p,y:v};return p=R.x,v=R.y,l?Object.assign({},O,((C={})[y]=x?"0":"",C[g]=b?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+v+"px)":"translate3d("+p+"px, "+v+"px, 0)",C)):Object.assign({},O,((t={})[y]=x?v+"px":"",t[g]=b?p+"px":"",t.transform="",t))}const tr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,a=n.adaptive,s=void 0===a||a,i=n.roundOffsets,l=void 0===i||i,c={placement:mn(t.placement),variation:Jn(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,er(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,er(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var nr={passive:!0};const rr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,o=r.scroll,a=void 0===o||o,s=r.resize,i=void 0===s||s,l=vn(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach((function(e){e.addEventListener("scroll",n.update,nr)})),i&&l.addEventListener("resize",n.update,nr),function(){a&&c.forEach((function(e){e.removeEventListener("scroll",n.update,nr)})),i&&l.removeEventListener("resize",n.update,nr)}},data:{}};var or={left:"right",right:"left",bottom:"top",top:"bottom"};function ar(e){return e.replace(/left|right|bottom|top/g,(function(e){return or[e]}))}var sr={start:"end",end:"start"};function ir(e){return e.replace(/start|end/g,(function(e){return sr[e]}))}function lr(e){var t=vn(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function cr(e){return jn(Tn(e)).left+lr(e).scrollLeft}function ur(e){var t=kn(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function dr(e){return["html","body","#document"].indexOf(Rn(e))>=0?e.ownerDocument.body:bn(e)&&ur(e)?e:dr(Sn(e))}function fr(e,t){var n;void 0===t&&(t=[]);var r=dr(e),o=r===(null==(n=e.ownerDocument)?void 0:n.body),a=vn(r),s=o?[a].concat(a.visualViewport||[],ur(r)?r:[]):r,i=t.concat(s);return o?i:i.concat(fr(Sn(s)))}function pr(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function mr(e,t,n){return t===Un?pr(function(e,t){var n=vn(e),r=Tn(e),o=n.visualViewport,a=r.clientWidth,s=r.clientHeight,i=0,l=0;if(o){a=o.width,s=o.height;var c=En();(c||!c&&"fixed"===t)&&(i=o.offsetLeft,l=o.offsetTop)}return{width:a,height:s,x:i+cr(e),y:l}}(e,n)):hn(t)?function(e,t){var n=jn(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):pr(function(e){var t,n=Tn(e),r=lr(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=gn(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=gn(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),i=-r.scrollLeft+cr(e),l=-r.scrollTop;return"rtl"===kn(o||n).direction&&(i+=gn(n.clientWidth,o?o.clientWidth:0)-a),{width:a,height:s,x:i,y:l}}(Tn(e)))}function vr(e){var t,n=e.reference,r=e.element,o=e.placement,a=o?mn(o):null,s=o?Jn(o):null,i=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(a){case Bn:t={x:i,y:n.y-r.height};break;case Fn:t={x:i,y:n.y+n.height};break;case _n:t={x:n.x+n.width,y:l};break;case Hn:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var c=a?Mn(a):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case Vn:t[c]=t[c]-(n[u]/2-r[u]/2);break;case zn:t[c]=t[c]+(n[u]/2-r[u]/2)}}return t}function hr(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=void 0===r?e.placement:r,a=n.strategy,s=void 0===a?e.strategy:a,i=n.boundary,l=void 0===i?"clippingParents":i,c=n.rootBoundary,u=void 0===c?Un:c,d=n.elementContext,f=void 0===d?qn:d,p=n.altBoundary,m=void 0!==p&&p,v=n.padding,h=void 0===v?0:v,b=An("number"!=typeof h?h:Ln(h,Wn)),x=f===qn?"reference":qn,g=e.rects.popper,y=e.elements[m?x:f],w=function(e,t,n,r){var o="clippingParents"===t?function(e){var t=fr(Sn(e)),n=["absolute","fixed"].indexOf(kn(e).position)>=0&&bn(e)?Dn(e):e;return hn(n)?t.filter((function(e){return hn(e)&&On(e,n)&&"body"!==Rn(e)})):[]}(e):[].concat(t),a=[].concat(o,[n]),s=a[0],i=a.reduce((function(t,n){var o=mr(e,n,r);return t.top=gn(o.top,t.top),t.right=yn(o.right,t.right),t.bottom=yn(o.bottom,t.bottom),t.left=gn(o.left,t.left),t}),mr(e,s,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}(hn(y)?y:y.contextElement||Tn(e.elements.popper),l,u,s),N=jn(e.elements.reference),E=vr({reference:N,element:g,strategy:"absolute",placement:o}),j=pr(Object.assign({},g,E)),C=f===qn?j:N,O={top:w.top-C.top+b.top,bottom:C.bottom-w.bottom+b.bottom,left:w.left-C.left+b.left,right:C.right-w.right+b.right},R=e.modifiersData.offset;if(f===qn&&R){var k=R[o];Object.keys(O).forEach((function(e){var t=[_n,Fn].indexOf(e)>=0?1:-1,n=[Bn,Fn].indexOf(e)>=0?"y":"x";O[e]+=k[n]*t}))}return O}const br={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,a=void 0===o||o,s=n.altAxis,i=void 0===s||s,l=n.fallbackPlacements,c=n.padding,u=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,v=n.allowedAutoPlacements,h=t.options.placement,b=mn(h),x=l||(b!==h&&m?function(e){if(mn(e)===Kn)return[];var t=ar(e);return[ir(e),t,ir(t)]}(h):[ar(h)]),g=[h].concat(x).reduce((function(e,n){return e.concat(mn(n)===Kn?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,a=n.rootBoundary,s=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?Xn:l,u=Jn(r),d=u?i?Gn:Gn.filter((function(e){return Jn(e)===u})):Wn,f=d.filter((function(e){return c.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,n){return t[n]=hr(e,{placement:n,boundary:o,rootBoundary:a,padding:s})[mn(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:v}):n)}),[]),y=t.rects.reference,w=t.rects.popper,N=new Map,E=!0,j=g[0],C=0;C<g.length;C++){var O=g[C],R=mn(O),k=Jn(O)===Vn,P=[Bn,Fn].indexOf(R)>=0,T=P?"width":"height",S=hr(t,{placement:O,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),$=P?k?_n:Hn:k?Fn:Bn;y[T]>w[T]&&($=ar($));var D=ar($),M=[];if(a&&M.push(S[R]<=0),i&&M.push(S[$]<=0,S[D]<=0),M.every((function(e){return e}))){j=O,E=!1;break}N.set(O,M)}if(E)for(var I=function(e){var t=g.find((function(t){var n=N.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return j=t,"break"},A=m?3:1;A>0&&"break"!==I(A);A--);t.placement!==j&&(t.modifiersData[r]._skip=!0,t.placement=j,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xr(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function gr(e){return[Bn,_n,Fn,Hn].some((function(t){return e[t]>=0}))}const yr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,a=void 0===o?[0,0]:o,s=Xn.reduce((function(e,n){return e[n]=function(e,t,n){var r=mn(e),o=[Hn,Bn].indexOf(r)>=0?-1:1,a="function"==typeof n?n(Object.assign({},t,{placement:e})):n,s=a[0],i=a[1];return s=s||0,i=(i||0)*o,[Hn,_n].indexOf(r)>=0?{x:i,y:s}:{x:s,y:i}}(n,t.rects,a),e}),{}),i=s[t.placement],l=i.x,c=i.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=s}},wr={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,a=void 0===o||o,s=n.altAxis,i=void 0!==s&&s,l=n.boundary,c=n.rootBoundary,u=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,v=void 0===m?0:m,h=hr(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),b=mn(t.placement),x=Jn(t.placement),g=!x,y=Mn(b),w="x"===y?"y":"x",N=t.modifiersData.popperOffsets,E=t.rects.reference,j=t.rects.popper,C="function"==typeof v?v(Object.assign({},t.rects,{placement:t.placement})):v,O="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,k={x:0,y:0};if(N){if(a){var P,T="y"===y?Bn:Hn,S="y"===y?Fn:_n,$="y"===y?"height":"width",D=N[y],M=D+h[T],I=D-h[S],A=p?-j[$]/2:0,L=x===Vn?E[$]:j[$],B=x===Vn?-j[$]:-E[$],F=t.elements.arrow,_=p&&F?Cn(F):{width:0,height:0},H=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},K=H[T],W=H[S],V=In(0,E[$],_[$]),z=g?E[$]/2-A-V-K-O.mainAxis:L-V-K-O.mainAxis,U=g?-E[$]/2+A+V+W+O.mainAxis:B+V+W+O.mainAxis,q=t.elements.arrow&&Dn(t.elements.arrow),G=q?"y"===y?q.clientTop||0:q.clientLeft||0:0,X=null!=(P=null==R?void 0:R[y])?P:0,Y=D+U-X,Z=In(p?yn(M,D+z-X-G):M,D,p?gn(I,Y):I);N[y]=Z,k[y]=Z-D}if(i){var J,Q="x"===y?Bn:Hn,ee="x"===y?Fn:_n,te=N[w],ne="y"===w?"height":"width",re=te+h[Q],oe=te-h[ee],ae=-1!==[Bn,Hn].indexOf(b),se=null!=(J=null==R?void 0:R[w])?J:0,ie=ae?re:te-E[ne]-j[ne]-se+O.altAxis,le=ae?te+E[ne]+j[ne]-se-O.altAxis:oe,ce=p&&ae?function(e,t,n){var r=In(e,t,n);return r>n?n:r}(ie,te,le):In(p?ie:re,te,p?le:oe);N[w]=ce,k[w]=ce-te}t.modifiersData[r]=k}},requiresIfExists:["offset"]};function Nr(e,t,n){void 0===n&&(n=!1);var r,o,a=bn(t),s=bn(t)&&function(e){var t=e.getBoundingClientRect(),n=wn(t.width)/e.offsetWidth||1,r=wn(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),i=Tn(t),l=jn(e,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(a||!a&&!n)&&(("body"!==Rn(t)||ur(i))&&(c=(r=t)!==vn(r)&&bn(r)?{scrollLeft:(o=r).scrollLeft,scrollTop:o.scrollTop}:lr(r)),bn(t)?((u=jn(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):i&&(u.x=cr(i))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function Er(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}var jr={placement:"bottom",modifiers:[],strategy:"absolute"};function Cr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}const Or=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,o=t.defaultOptions,a=void 0===o?jr:o;return function(e,t,n){void 0===n&&(n=a);var o,s,i={placement:"bottom",orderedModifiers:[],options:Object.assign({},jr,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:i,setOptions:function(n){var o="function"==typeof n?n(i.options):n;d(),i.options=Object.assign({},a,i.options,o),i.scrollParents={reference:hn(e)?fr(e):e.contextElement?fr(e.contextElement):[],popper:fr(t)};var s,c,f=function(e){var t=Er(e);return Yn.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((s=[].concat(r,i.options.modifiers),c=s.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return i.orderedModifiers=f.filter((function(e){return e.enabled})),i.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var a=o({state:i,name:t,instance:u,options:r});l.push(a||function(){})}})),u.update()},forceUpdate:function(){if(!c){var e=i.elements,t=e.reference,n=e.popper;if(Cr(t,n)){i.rects={reference:Nr(t,Dn(n),"fixed"===i.options.strategy),popper:Cn(n)},i.reset=!1,i.placement=i.options.placement,i.orderedModifiers.forEach((function(e){return i.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<i.orderedModifiers.length;r++)if(!0!==i.reset){var o=i.orderedModifiers[r],a=o.fn,s=o.options,l=void 0===s?{}:s,d=o.name;"function"==typeof a&&(i=a({state:i,options:l,name:d,instance:u})||i)}else i.reset=!1,r=-1}}},update:(o=function(){return new Promise((function(e){u.forceUpdate(),e(i)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(o())}))}))),s}),destroy:function(){d(),c=!0}};if(!Cr(e,t))return u;function d(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}({defaultModifiers:[{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,s=hr(t,{elementContext:"reference"}),i=hr(t,{altBoundary:!0}),l=xr(s,r),c=xr(i,o,a),u=gr(l),d=gr(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=vr({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},tr,rr,yr,br,wr,Zn]}),Rr=["enabled","placement","strategy","modifiers"],kr={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>{}},Pr={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:({state:e})=>()=>{const{reference:t,popper:n}=e.elements;if("removeAttribute"in t){const e=(t.getAttribute("aria-describedby")||"").split(",").filter((e=>e.trim()!==n.id));e.length?t.setAttribute("aria-describedby",e.join(",")):t.removeAttribute("aria-describedby")}},fn:({state:e})=>{var t;const{popper:n,reference:r}=e.elements,o=null==(t=n.getAttribute("role"))?void 0:t.toLowerCase();if(n.id&&"tooltip"===o&&"setAttribute"in r){const e=r.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(n.id))return;r.setAttribute("aria-describedby",e?`${e},${n.id}`:n.id)}}},Tr=[],Sr=function(e,t,r={}){let{enabled:o=!0,placement:a="bottom",strategy:s="absolute",modifiers:i=Tr}=r,l=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(r,Rr);const c=(0,n.useRef)(i),u=(0,n.useRef)(),d=(0,n.useCallback)((()=>{var e;null==(e=u.current)||e.update()}),[]),f=(0,n.useCallback)((()=>{var e;null==(e=u.current)||e.forceUpdate()}),[]),[p,m]=(b=(0,n.useState)({placement:a,update:d,forceUpdate:f,attributes:{},styles:{popper:{},arrow:{}}}),x=Me(),[b[0],(0,n.useCallback)((function(e){if(x())return b[1](e)}),[x,b[1]])]),v=(0,n.useMemo)((()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:({state:e})=>{const t={},n={};Object.keys(e.elements).forEach((r=>{t[r]=e.styles[r],n[r]=e.attributes[r]})),m({state:e,styles:t,attributes:n,update:d,forceUpdate:f,placement:e.placement})}})),[d,f,m]),h=(0,n.useMemo)((()=>(pn(c.current,i)||(c.current=i),c.current)),[i]);var b,x;return(0,n.useEffect)((()=>{u.current&&o&&u.current.setOptions({placement:a,strategy:s,modifiers:[...h,v,kr]})}),[s,a,v,o,h]),(0,n.useEffect)((()=>{if(o&&null!=e&&null!=t)return u.current=Or(e,t,Object.assign({},l,{placement:a,strategy:s,modifiers:[...h,Pr,v]})),()=>{null!=u.current&&(u.current.destroy(),u.current=void 0,m((e=>Object.assign({},e,{attributes:{},styles:{popper:{}}}))))}}),[o,e,t]),p};function $r(e,t){return e.contains?e.contains(t):e.compareDocumentPosition?e===t||!!(16&e.compareDocumentPosition(t)):void 0}var Dr=o(459),Mr=o.n(Dr);const Ir=()=>{},Ar=e=>e&&("current"in e?e.current:e),Lr={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"},Br=function(e,t=Ir,{disabled:r,clickTrigger:o="click"}={}){const a=(0,n.useRef)(!1),s=(0,n.useRef)(!1),i=(0,n.useCallback)((t=>{const n=Ar(e);var r;Mr()(!!n,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),a.current=!n||!!((r=t).metaKey||r.altKey||r.ctrlKey||r.shiftKey)||!function(e){return 0===e.button}(t)||!!$r(n,t.target)||s.current,s.current=!1}),[e]),l=ke((t=>{const n=Ar(e);n&&$r(n,t.target)&&(s.current=!0)})),c=ke((e=>{a.current||t(e)}));(0,n.useEffect)((()=>{var t,n;if(r||null==e)return;const a=C(Ar(e)),s=a.defaultView||window;let u=null!=(t=s.event)?t:null==(n=s.parent)?void 0:n.event,d=null;Lr[o]&&(d=X(a,Lr[o],l,!0));const f=X(a,o,i,!0),p=X(a,o,(e=>{e!==u?c(e):u=void 0}));let m=[];return"ontouchstart"in a.documentElement&&(m=[].slice.call(a.body.children).map((e=>X(e,"mousemove",Ir)))),()=>{null==d||d(),f(),p(),m.forEach((e=>e()))}}),[e,r,o,i,l,c])};function Fr(e={}){return Array.isArray(e)?e:Object.keys(e).map((t=>(e[t].name=t,e[t])))}function _r({enabled:e,enableEvents:t,placement:n,flip:r,offset:o,fixed:a,containerPadding:s,arrowElement:i,popperConfig:l={}}){var c,u,d,f,p;const m=function(e){const t={};return Array.isArray(e)?(null==e||e.forEach((e=>{t[e.name]=e})),t):e||t}(l.modifiers);return Object.assign({},l,{placement:n,enabled:e,strategy:a?"fixed":l.strategy,modifiers:Fr(Object.assign({},m,{eventListeners:{enabled:t,options:null==(c=m.eventListeners)?void 0:c.options},preventOverflow:Object.assign({},m.preventOverflow,{options:s?Object.assign({padding:s},null==(u=m.preventOverflow)?void 0:u.options):null==(d=m.preventOverflow)?void 0:d.options}),offset:{options:Object.assign({offset:o},null==(f=m.offset)?void 0:f.options)},arrow:Object.assign({},m.arrow,{enabled:!!i,options:Object.assign({},null==(p=m.arrow)?void 0:p.options,{element:i})}),flip:Object.assign({enabled:!!r},m.flip)}))})}const Hr=["children"],Kr=()=>{};function Wr(e={}){const t=(0,n.useContext)(un),[r,o]=De(),a=(0,n.useRef)(!1),{flip:s,offset:i,rootCloseEvent:l,fixed:c=!1,placement:u,popperConfig:d={},enableEventListeners:f=!0,usePopper:p=!!t}=e,m=null==(null==t?void 0:t.show)?!!e.show:t.show;m&&!a.current&&(a.current=!0);const{placement:v,setMenu:h,menuElement:b,toggleElement:x}=t||{},g=Sr(x,b,_r({placement:u||v||"bottom-start",enabled:p,enableEvents:null==f?m:f,offset:i,flip:s,fixed:c,arrowElement:r,popperConfig:d})),y=Object.assign({ref:h||Kr,"aria-labelledby":null==x?void 0:x.id},g.attributes.popper,{style:g.styles.popper}),w={show:m,placement:v,hasShown:a.current,toggle:null==t?void 0:t.toggle,popper:p?g:null,arrowProps:p?Object.assign({ref:o},g.attributes.arrow,{style:g.styles.arrow}):{}};return Br(b,(e=>{null==t||t.toggle(!1,e)}),{clickTrigger:l,disabled:!m}),[y,w]}function Vr(e){let{children:t}=e,n=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,Hr);const[r,o]=Wr(n);return(0,m.jsx)(m.Fragment,{children:t(r,o)})}Vr.displayName="DropdownMenu",Vr.defaultProps={usePopper:!0};const zr=Vr,Ur={prefix:String(Math.round(1e10*Math.random())),current:0,isSSR:!1},qr=n.createContext(Ur);let Gr=Boolean("undefined"!=typeof window&&window.document&&window.document.createElement),Xr=new WeakMap;function Yr(e=!1){let t=(0,n.useContext)(qr),r=(0,n.useRef)(null);if(null===r.current&&!e){var o,a;let e=null===(o=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===o||null===(a=o.ReactCurrentOwner)||void 0===a?void 0:a.current;if(e){let n=Xr.get(e);null==n?Xr.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==n.state&&(t.current=n.id,Xr.delete(e))}r.current=++t.current}return r.current}function Zr(e){let t=(0,n.useContext)(qr);t!==Ur||Gr||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let r=Yr(!!e);return e||`react-aria${t.prefix}-${r}`}const Jr=e=>{var t;return"menu"===(null==(t=e.getAttribute("role"))?void 0:t.toLowerCase())},Qr=()=>{};function eo(){const e=Zr(),{show:t=!1,toggle:r=Qr,setToggle:o,menuElement:a}=(0,n.useContext)(un)||{},s=(0,n.useCallback)((e=>{r(!t,e)}),[t,r]),i={id:e,ref:o||Qr,onClick:s,"aria-expanded":!!t};return a&&Jr(a)&&(i["aria-haspopup"]=!0),[i,{show:t,toggle:r}]}function to({children:e}){const[t,n]=eo();return(0,m.jsx)(m.Fragment,{children:e(t,n)})}to.displayName="DropdownToggle";const no=to,ro=(e,t=null)=>null!=e?String(e):t||null,oo=n.createContext(null),ao=n.createContext(null);ao.displayName="NavContext";const so=ao,io="data-rr-ui-";function lo(e){return`${io}${e}`}const co=["eventKey","disabled","onClick","active","as"];function uo({key:e,href:t,active:r,disabled:o,onClick:a}){const s=(0,n.useContext)(oo),i=(0,n.useContext)(so),{activeKey:l}=i||{},c=ro(e,t),u=null==r&&null!=e?ro(l)===c:r;return[{onClick:ke((e=>{o||(null==a||a(e),s&&!e.isPropagationStopped()&&s(c,e))})),"aria-disabled":o||void 0,"aria-selected":u,[lo("dropdown-item")]:""},{isActive:u}]}const fo=n.forwardRef(((e,t)=>{let{eventKey:n,disabled:r,onClick:o,active:a,as:s=He}=e,i=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,co);const[l]=uo({key:n,href:i.href,disabled:r,onClick:o,active:a});return(0,m.jsx)(s,Object.assign({},i,{ref:t},l))}));fo.displayName="DropdownItem";const po=fo,mo=(0,n.createContext)(W?window:void 0);function vo(){return(0,n.useContext)(mo)}function ho(){const e=cn(),t=(0,n.useRef)(null),r=(0,n.useCallback)((n=>{t.current=n,e()}),[e]);return[t,r]}function bo({defaultShow:e,show:t,onSelect:r,onToggle:o,itemSelector:a=`* [${lo("dropdown-item")}]`,focusFirstItemOnShow:s,placement:i="bottom-start",children:l}){const c=vo(),[u,d]=ln(t,e,o),[f,p]=ho(),v=f.current,[h,b]=ho(),x=h.current,g=Ie(u),y=(0,n.useRef)(null),w=(0,n.useRef)(!1),N=(0,n.useContext)(oo),E=(0,n.useCallback)(((e,t,n=(null==t?void 0:t.type))=>{d(e,{originalEvent:t,source:n})}),[d]),j=ke(((e,t)=>{null==r||r(e,t),E(!1,t,"select"),t.isPropagationStopped()||null==N||N(e,t)})),C=(0,n.useMemo)((()=>({toggle:E,placement:i,show:u,menuElement:v,toggleElement:x,setMenu:p,setToggle:b})),[E,i,u,v,x,p,b]);v&&g&&!u&&(w.current=v.contains(v.ownerDocument.activeElement));const O=ke((()=>{x&&x.focus&&x.focus()})),R=ke((()=>{const e=y.current;let t=s;if(null==t&&(t=!(!f.current||!Jr(f.current))&&"keyboard"),!1===t||"keyboard"===t&&!/^key.+$/.test(e))return;const n=sn(f.current,a)[0];n&&n.focus&&n.focus()}));(0,n.useEffect)((()=>{u?R():w.current&&(w.current=!1,O())}),[u,w,O,R]),(0,n.useEffect)((()=>{y.current=null}));const k=(e,t)=>{if(!f.current)return null;const n=sn(f.current,a);let r=n.indexOf(e)+t;return r=Math.max(0,Math.min(r,n.length)),n[r]};return function(e,t,r,o){void 0===o&&(o=!1);var a=ke((e=>{var t,n;const{key:r}=e,o=e.target,a=null==(t=f.current)?void 0:t.contains(o),s=null==(n=h.current)?void 0:n.contains(o);if(/input|textarea/i.test(o.tagName)&&(" "===r||"Escape"!==r&&a||"Escape"===r&&"search"===o.type))return;if(!a&&!s)return;if(!("Tab"!==r||f.current&&u))return;y.current=e.type;const i={originalEvent:e,source:e.type};switch(r){case"ArrowUp":{const t=k(o,-1);return t&&t.focus&&t.focus(),void e.preventDefault()}case"ArrowDown":if(e.preventDefault(),u){const e=k(o,1);e&&e.focus&&e.focus()}else d(!0,i);return;case"Tab":q(o.ownerDocument,"keyup",(e=>{var t;("Tab"!==e.key||e.target)&&null!=(t=f.current)&&t.contains(e.target)||d(!1,i)}),{once:!0});break;case"Escape":"Escape"===r&&(e.preventDefault(),e.stopPropagation()),d(!1,i)}}));(0,n.useEffect)((function(){var n="function"==typeof e?e():e;return n.addEventListener(t,a,o),function(){return n.removeEventListener(t,a,o)}}),[e])}((0,n.useCallback)((()=>c.document),[c]),"keydown"),(0,m.jsx)(oo.Provider,{value:j,children:(0,m.jsx)(un.Provider,{value:C,children:l})})}mo.Provider,bo.displayName="Dropdown",bo.Menu=zr,bo.Toggle=no,bo.Item=po;const xo=bo,go=n.createContext({});go.displayName="DropdownContext";const yo=go,wo=n.forwardRef((({className:e,bsPrefix:n,as:r="hr",role:o="separator",...a},s)=>(n=y(n,"dropdown-divider"),(0,m.jsx)(r,{ref:s,className:t()(e,n),role:o,...a}))));wo.displayName="DropdownDivider";const No=wo,Eo=n.forwardRef((({className:e,bsPrefix:n,as:r="div",role:o="heading",...a},s)=>(n=y(n,"dropdown-header"),(0,m.jsx)(r,{ref:s,className:t()(e,n),role:o,...a}))));Eo.displayName="DropdownHeader";const jo=Eo,Co=n.forwardRef((({bsPrefix:e,className:n,eventKey:r,disabled:o=!1,onClick:a,active:s,as:i=Ve,...l},c)=>{const u=y(e,"dropdown-item"),[d,f]=uo({key:r,href:l.href,disabled:o,onClick:a,active:s});return(0,m.jsx)(i,{...l,...d,ref:c,className:t()(n,u,f.isActive&&"active",o&&"disabled")})}));Co.displayName="DropdownItem";const Oo=Co,Ro=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=y(n,"dropdown-item-text"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ro.displayName="DropdownItemText";const ko=Ro,Po=n.createContext(null);Po.displayName="InputGroupContext";const To=Po,So=n.createContext(null);So.displayName="NavbarContext";const $o=So;function Do(e,t){return e}function Mo(e,t,n){let r=e?n?"bottom-start":"bottom-end":n?"bottom-end":"bottom-start";return"up"===t?r=e?n?"top-start":"top-end":n?"top-end":"top-start":"end"===t?r=e?n?"left-end":"right-end":n?"left-start":"right-start":"start"===t?r=e?n?"right-end":"left-end":n?"right-start":"left-start":"down-centered"===t?r="bottom":"up-centered"===t&&(r="top"),r}const Io=n.forwardRef((({bsPrefix:e,className:r,align:o,rootCloseEvent:a,flip:s=!0,show:i,renderOnMount:l,as:c="div",popperConfig:u,variant:d,...f},p)=>{let v=!1;const h=(0,n.useContext)($o),b=y(e,"dropdown-menu"),{align:x,drop:g,isRTL:w}=(0,n.useContext)(yo);o=o||x;const N=(0,n.useContext)(To),E=[];if(o)if("object"==typeof o){const e=Object.keys(o);if(e.length){const t=e[0],n=o[t];v="start"===n,E.push(`${b}-${t}-${n}`)}}else"end"===o&&(v=!0);const j=Mo(v,g,w),[C,{hasShown:O,popper:R,show:k,toggle:P}]=Wr({flip:s,rootCloseEvent:a,show:i,usePopper:!h&&0===E.length,offset:[0,2],popperConfig:u,placement:j});if(C.ref=ne(Do(p),C.ref),Le((()=>{k&&(null==R||R.update())}),[k]),!O&&!l&&!N)return null;"string"!=typeof c&&(C.show=k,C.close=()=>null==P?void 0:P(!1),C.align=o);let T=f.style;return null!=R&&R.placement&&(T={...f.style,...C.style},f["x-placement"]=R.placement),(0,m.jsx)(c,{...f,...C,style:T,...(E.length||h)&&{"data-bs-popper":"static"},className:t()(r,b,k&&"show",v&&`${b}-end`,d&&`${b}-${d}`,...E)})}));Io.displayName="DropdownMenu";const Ao=Io,Lo=n.forwardRef((({bsPrefix:e,split:r,className:o,childBsPrefix:a,as:s=dt,...i},l)=>{const c=y(e,"dropdown-toggle"),u=(0,n.useContext)(un);void 0!==a&&(i.bsPrefix=a);const[d]=eo();return d.ref=ne(d.ref,Do(l)),(0,m.jsx)(s,{className:t()(o,c,r&&`${c}-split`,(null==u?void 0:u.show)&&"show"),...d,...i})}));Lo.displayName="DropdownToggle";const Bo=Lo,Fo=n.forwardRef(((e,r)=>{const{bsPrefix:o,drop:a="down",show:s,className:i,align:l="start",onSelect:c,onToggle:u,focusFirstItemOnShow:d,as:f="div",navbar:v,autoClose:h=!0,...b}=p(e,{show:"onToggle"}),x=(0,n.useContext)(To),g=y(o,"dropdown"),w=E(),N=ke(((e,t)=>{var n,r,o;(null==(n=t.originalEvent)||null==(r=n.target)?void 0:r.classList.contains("dropdown-toggle"))&&"mousedown"===t.source||(t.originalEvent.currentTarget!==document||"keydown"===t.source&&"Escape"!==t.originalEvent.key||(t.source="rootClose"),o=t.source,(!1===h?"click"===o:"inside"===h?"rootClose"!==o:"outside"!==h||"select"!==o)&&(null==u||u(e,t)))})),j=Mo("end"===l,a,w),C=(0,n.useMemo)((()=>({align:l,drop:a,isRTL:w})),[l,a,w]),O={down:g,"down-centered":`${g}-center`,up:"dropup","up-centered":"dropup-center dropup",end:"dropend",start:"dropstart"};return(0,m.jsx)(yo.Provider,{value:C,children:(0,m.jsx)(xo,{placement:j,show:s,onSelect:c,onToggle:N,focusFirstItemOnShow:d,itemSelector:`.${g}-item:not(.disabled):not(:disabled)`,children:x?b.children:(0,m.jsx)(f,{...b,ref:r,className:t()(i,s&&"show",O[a])})})})}));Fo.displayName="Dropdown";const _o=Object.assign(Fo,{Toggle:Bo,Menu:Ao,Item:Oo,ItemText:ko,Divider:No,Header:jo}),Ho=Ze().oneOf(["start","end"]),Ko=Ze().oneOfType([Ho,Ze().shape({sm:Ho}),Ze().shape({md:Ho}),Ze().shape({lg:Ho}),Ze().shape({xl:Ho}),Ze().shape({xxl:Ho}),Ze().object]),Wo={id:Ze().string,href:Ze().string,onClick:Ze().func,title:Ze().node.isRequired,disabled:Ze().bool,align:Ko,menuRole:Ze().string,renderMenuOnMount:Ze().bool,rootCloseEvent:Ze().string,menuVariant:Ze().oneOf(["dark"]),flip:Ze().bool,bsPrefix:Ze().string,variant:Ze().string,size:Ze().string},Vo=n.forwardRef((({title:e,children:t,bsPrefix:n,rootCloseEvent:r,variant:o,size:a,menuRole:s,renderMenuOnMount:i,disabled:l,href:c,id:u,menuVariant:d,flip:f,...p},v)=>(0,m.jsxs)(_o,{ref:v,...p,children:[(0,m.jsx)(Bo,{id:u,href:c,size:a,variant:o,disabled:l,childBsPrefix:n,children:e}),(0,m.jsx)(Ao,{role:s,renderOnMount:i,rootCloseEvent:r,variant:d,flip:f,children:t})]})));Vo.displayName="DropdownButton",Vo.propTypes=Wo;const zo=Vo,Uo={bsPrefix:Ze().string,fluid:Ze().bool,rounded:Ze().bool,roundedCircle:Ze().bool,thumbnail:Ze().bool},qo=n.forwardRef((({bsPrefix:e,className:n,fluid:r=!1,rounded:o=!1,roundedCircle:a=!1,thumbnail:s=!1,...i},l)=>(e=y(e,"img"),(0,m.jsx)("img",{ref:l,...i,className:t()(n,r&&`${e}-fluid`,o&&"rounded",a&&"rounded-circle",s&&`${e}-thumbnail`)}))));qo.displayName="Image";const Go=qo,Xo=n.forwardRef((({className:e,fluid:n=!0,...r},o)=>(0,m.jsx)(Go,{ref:o,...r,fluid:n,className:t()(e,"figure-img")})));Xo.displayName="FigureImage",Xo.propTypes=Uo;const Yo=Xo,Zo=n.forwardRef((({className:e,bsPrefix:n,as:r="figcaption",...o},a)=>(n=y(n,"figure-caption"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Zo.displayName="FigureCaption";const Jo=Zo,Qo=n.forwardRef((({className:e,bsPrefix:n,as:r="figure",...o},a)=>(n=y(n,"figure"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Qo.displayName="Figure";const ea=Object.assign(Qo,{Image:Yo,Caption:Jo}),ta={type:Ze().string,tooltip:Ze().bool,as:Ze().elementType},na=n.forwardRef((({as:e="div",className:n,type:r="valid",tooltip:o=!1,...a},s)=>(0,m.jsx)(e,{...a,ref:s,className:t()(n,`${r}-${o?"tooltip":"feedback"}`)})));na.displayName="Feedback",na.propTypes=ta;const ra=na,oa=n.createContext({}),aa=n.forwardRef((({id:e,bsPrefix:r,className:o,type:a="checkbox",isValid:s=!1,isInvalid:i=!1,as:l="input",...c},u)=>{const{controlId:d}=(0,n.useContext)(oa);return r=y(r,"form-check-input"),(0,m.jsx)(l,{...c,ref:u,type:a,id:e||d,className:t()(o,r,s&&"is-valid",i&&"is-invalid")})}));aa.displayName="FormCheckInput";const sa=aa,ia=n.forwardRef((({bsPrefix:e,className:r,htmlFor:o,...a},s)=>{const{controlId:i}=(0,n.useContext)(oa);return e=y(e,"form-check-label"),(0,m.jsx)("label",{...a,ref:s,htmlFor:o||i,className:t()(r,e)})}));ia.displayName="FormCheckLabel";const la=ia,ca=n.forwardRef((({id:e,bsPrefix:r,bsSwitchPrefix:o,inline:a=!1,reverse:s=!1,disabled:i=!1,isValid:l=!1,isInvalid:c=!1,feedbackTooltip:u=!1,feedback:d,feedbackType:f,className:p,style:v,title:h="",type:b="checkbox",label:x,children:g,as:w="input",...N},E)=>{r=y(r,"form-check"),o=y(o,"form-switch");const{controlId:j}=(0,n.useContext)(oa),C=(0,n.useMemo)((()=>({controlId:e||j})),[j,e]),O=!g&&null!=x&&!1!==x||function(e,t){return n.Children.toArray(e).some((e=>n.isValidElement(e)&&e.type===t))}(g,la),R=(0,m.jsx)(sa,{...N,type:"switch"===b?"checkbox":b,ref:E,isValid:l,isInvalid:c,disabled:i,as:w});return(0,m.jsx)(oa.Provider,{value:C,children:(0,m.jsx)("div",{style:v,className:t()(p,O&&r,a&&`${r}-inline`,s&&`${r}-reverse`,"switch"===b&&o),children:g||(0,m.jsxs)(m.Fragment,{children:[R,O&&(0,m.jsx)(la,{title:h,children:x}),d&&(0,m.jsx)(ra,{type:f,tooltip:u,children:d})]})})})}));ca.displayName="FormCheck";const ua=Object.assign(ca,{Input:sa,Label:la}),da=n.forwardRef((({bsPrefix:e,type:r,size:o,htmlSize:a,id:s,className:i,isValid:l=!1,isInvalid:c=!1,plaintext:u,readOnly:d,as:f="input",...p},v)=>{const{controlId:h}=(0,n.useContext)(oa);return e=y(e,"form-control"),(0,m.jsx)(f,{...p,type:r,size:a,ref:v,readOnly:d,id:s||h,className:t()(i,u?`${e}-plaintext`:e,o&&`${e}-${o}`,"color"===r&&`${e}-color`,l&&"is-valid",c&&"is-invalid")})}));da.displayName="FormControl";const fa=Object.assign(da,{Feedback:ra}),pa=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"form-floating"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));pa.displayName="FormFloating";const ma=pa,va=n.forwardRef((({controlId:e,as:t="div",...r},o)=>{const a=(0,n.useMemo)((()=>({controlId:e})),[e]);return(0,m.jsx)(oa.Provider,{value:a,children:(0,m.jsx)(t,{...r,ref:o})})}));va.displayName="FormGroup";const ha=va,ba=n.forwardRef((({as:e="label",bsPrefix:r,column:o=!1,visuallyHidden:a=!1,className:s,htmlFor:i,...l},c)=>{const{controlId:u}=(0,n.useContext)(oa);r=y(r,"form-label");let d="col-form-label";"string"==typeof o&&(d=`${d} ${d}-${o}`);const f=t()(s,r,a&&"visually-hidden",o&&d);return i=i||u,o?(0,m.jsx)(nn,{ref:c,as:"label",className:f,htmlFor:i,...l}):(0,m.jsx)(e,{ref:c,className:f,htmlFor:i,...l})}));ba.displayName="FormLabel";const xa=ba,ga=n.forwardRef((({bsPrefix:e,className:r,id:o,...a},s)=>{const{controlId:i}=(0,n.useContext)(oa);return e=y(e,"form-range"),(0,m.jsx)("input",{...a,type:"range",ref:s,className:t()(r,e),id:o||i})}));ga.displayName="FormRange";const ya=ga,wa=n.forwardRef((({bsPrefix:e,size:r,htmlSize:o,className:a,isValid:s=!1,isInvalid:i=!1,id:l,...c},u)=>{const{controlId:d}=(0,n.useContext)(oa);return e=y(e,"form-select"),(0,m.jsx)("select",{...c,size:o,ref:u,className:t()(a,e,r&&`${e}-${r}`,s&&"is-valid",i&&"is-invalid"),id:l||d})}));wa.displayName="FormSelect";const Na=wa,Ea=n.forwardRef((({bsPrefix:e,className:n,as:r="small",muted:o,...a},s)=>(e=y(e,"form-text"),(0,m.jsx)(r,{...a,ref:s,className:t()(n,e,o&&"text-muted")}))));Ea.displayName="FormText";const ja=Ea,Ca=n.forwardRef(((e,t)=>(0,m.jsx)(ua,{...e,ref:t,type:"switch"})));Ca.displayName="Switch";const Oa=Object.assign(Ca,{Input:ua.Input,Label:ua.Label}),Ra=n.forwardRef((({bsPrefix:e,className:n,children:r,controlId:o,label:a,...s},i)=>(e=y(e,"form-floating"),(0,m.jsxs)(ha,{ref:i,className:t()(n,e),controlId:o,...s,children:[r,(0,m.jsx)("label",{htmlFor:o,children:a})]}))));Ra.displayName="FloatingLabel";const ka=Ra,Pa={_ref:Ze().any,validated:Ze().bool,as:Ze().elementType},Ta=n.forwardRef((({className:e,validated:n,as:r="form",...o},a)=>(0,m.jsx)(r,{...o,ref:a,className:t()(e,n&&"was-validated")})));Ta.displayName="Form",Ta.propTypes=Pa;const Sa=Object.assign(Ta,{Group:ha,Control:fa,Floating:ma,Check:ua,Switch:Oa,Label:xa,Text:ja,Range:ya,Select:Na,FloatingLabel:ka}),$a=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=y(n,"input-group-text"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));$a.displayName="InputGroupText";const Da=$a,Ma=n.forwardRef((({bsPrefix:e,size:r,hasValidation:o,className:a,as:s="div",...i},l)=>{e=y(e,"input-group");const c=(0,n.useMemo)((()=>({})),[]);return(0,m.jsx)(To.Provider,{value:c,children:(0,m.jsx)(s,{ref:l,...i,className:t()(a,e,r&&`${e}-${r}`,o&&"has-validation")})})}));Ma.displayName="InputGroup";const Ia=Object.assign(Ma,{Text:Da,Radio:e=>(0,m.jsx)(Da,{children:(0,m.jsx)(sa,{type:"radio",...e})}),Checkbox:e=>(0,m.jsx)(Da,{children:(0,m.jsx)(sa,{type:"checkbox",...e})})}),Aa=n.createContext(null),La=["as","active","eventKey"];function Ba({key:e,onClick:t,active:r,id:o,role:a,disabled:s}){const i=(0,n.useContext)(oo),l=(0,n.useContext)(so),c=(0,n.useContext)(Aa);let u=r;const d={role:a};if(l){a||"tablist"!==l.role||(d.role="tab");const t=l.getControllerId(null!=e?e:null),n=l.getControlledId(null!=e?e:null);d[lo("event-key")]=e,d.id=t||o,u=null==r&&null!=e?l.activeKey===e:r,!u&&(null!=c&&c.unmountOnExit||null!=c&&c.mountOnEnter)||(d["aria-controls"]=n)}return"tab"===d.role&&(d["aria-selected"]=u,u||(d.tabIndex=-1),s&&(d.tabIndex=-1,d["aria-disabled"]=!0)),d.onClick=ke((n=>{s||(null==t||t(n),null!=e&&i&&!n.isPropagationStopped()&&i(e,n))})),[d,{isActive:u}]}const Fa=n.forwardRef(((e,t)=>{let{as:n=He,active:r,eventKey:o}=e,a=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,La);const[s,i]=Ba(Object.assign({key:ro(o,a.href),active:r},a));return s[lo("active")]=i.isActive,(0,m.jsx)(n,Object.assign({},a,s,{ref:t}))}));Fa.displayName="NavItem";const _a=Fa,Ha=["as","onSelect","activeKey","role","onKeyDown"],Ka=()=>{},Wa=lo("event-key"),Va=n.forwardRef(((e,t)=>{let{as:r="div",onSelect:o,activeKey:a,role:s,onKeyDown:i}=e,l=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,Ha);const c=cn(),u=(0,n.useRef)(!1),d=(0,n.useContext)(oo),f=(0,n.useContext)(Aa);let p,v;f&&(s=s||"tablist",a=f.activeKey,p=f.getControlledId,v=f.getControllerId);const h=(0,n.useRef)(null),b=e=>{const t=h.current;if(!t)return null;const n=sn(t,`[${Wa}]:not([aria-disabled=true])`),r=t.querySelector("[aria-selected=true]");if(!r||r!==document.activeElement)return null;const o=n.indexOf(r);if(-1===o)return null;let a=o+e;return a>=n.length&&(a=0),a<0&&(a=n.length-1),n[a]},x=(e,t)=>{null!=e&&(null==o||o(e,t),null==d||d(e,t))};(0,n.useEffect)((()=>{if(h.current&&u.current){const e=h.current.querySelector(`[${Wa}][aria-selected=true]`);null==e||e.focus()}u.current=!1}));const g=ne(t,h);return(0,m.jsx)(oo.Provider,{value:x,children:(0,m.jsx)(so.Provider,{value:{role:s,activeKey:ro(a),getControlledId:p||Ka,getControllerId:v||Ka},children:(0,m.jsx)(r,Object.assign({},l,{onKeyDown:e=>{if(null==i||i(e),!f)return;let t;switch(e.key){case"ArrowLeft":case"ArrowUp":t=b(-1);break;case"ArrowRight":case"ArrowDown":t=b(1);break;default:return}t&&(e.preventDefault(),x(t.dataset[("EventKey","rrUiEventKey")]||null,e),u.current=!0,c())},ref:g,role:s}))})})}));Va.displayName="Nav";const za=Object.assign(Va,{Item:_a}),Ua=n.forwardRef((({bsPrefix:e,active:n,disabled:r,eventKey:o,className:a,variant:s,action:i,as:l,...c},u)=>{e=y(e,"list-group-item");const[d,f]=Ba({key:ro(o,c.href),active:n,...c}),p=ke((e=>{if(r)return e.preventDefault(),void e.stopPropagation();d.onClick(e)}));r&&void 0===c.tabIndex&&(c.tabIndex=-1,c["aria-disabled"]=!0);const v=l||(i?c.href?"a":"button":"div");return(0,m.jsx)(v,{ref:u,...c,...d,onClick:p,className:t()(a,e,f.isActive&&"active",r&&"disabled",s&&`${e}-${s}`,i&&`${e}-action`)})}));Ua.displayName="ListGroupItem";const qa=Ua,Ga=n.forwardRef(((e,n)=>{const{className:r,bsPrefix:o,variant:a,horizontal:s,numbered:i,as:l="div",...c}=p(e,{activeKey:"onSelect"}),u=y(o,"list-group");let d;return s&&(d=!0===s?"horizontal":`horizontal-${s}`),(0,m.jsx)(za,{ref:n,...c,as:l,className:t()(r,u,a&&`${u}-${a}`,d&&`${u}-${d}`,i&&`${u}-numbered`)})}));Ga.displayName="ListGroup";const Xa=Object.assign(Ga,{Item:qa});var Ya;function Za(e){if((!Ya&&0!==Ya||e)&&W){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),Ya=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return Ya}function Ja(e){void 0===e&&(e=C());try{var t=e.activeElement;return t&&t.nodeName?t:null}catch(t){return e.body}}const Qa=lo("modal-open"),es=class{constructor({ownerDocument:e,handleContainerOverflow:t=!0,isRTL:n=!1}={}){this.handleContainerOverflow=t,this.isRTL=n,this.modals=[],this.ownerDocument=e}getScrollbarWidth(){return function(e=document){const t=e.defaultView;return Math.abs(t.innerWidth-e.documentElement.clientWidth)}(this.ownerDocument)}getElement(){return(this.ownerDocument||document).body}setModalAttributes(e){}removeModalAttributes(e){}setContainerStyle(e){const t={overflow:"hidden"},n=this.isRTL?"paddingLeft":"paddingRight",r=this.getElement();e.style={overflow:r.style.overflow,[n]:r.style[n]},e.scrollBarWidth&&(t[n]=`${parseInt(T(r,n)||"0",10)+e.scrollBarWidth}px`),r.setAttribute(Qa,""),T(r,t)}reset(){[...this.modals].forEach((e=>this.remove(e)))}removeContainerStyle(e){const t=this.getElement();t.removeAttribute(Qa),Object.assign(t.style,e.style)}add(e){let t=this.modals.indexOf(e);return-1!==t||(t=this.modals.length,this.modals.push(e),this.setModalAttributes(e),0!==t||(this.state={scrollBarWidth:this.getScrollbarWidth(),style:{}},this.handleContainerOverflow&&this.setContainerStyle(this.state))),t}remove(e){const t=this.modals.indexOf(e);-1!==t&&(this.modals.splice(t,1),!this.modals.length&&this.handleContainerOverflow&&this.removeContainerStyle(this.state),this.removeModalAttributes(e))}isTopModal(e){return!!this.modals.length&&this.modals[this.modals.length-1]===e}},ts=(e,t)=>W?null==e?(t||C()).body:("function"==typeof e&&(e=e()),e&&"current"in e&&(e=e.current),e&&("nodeType"in e||e.getBoundingClientRect)?e:null):null;function ns(e,t){const r=vo(),[o,a]=(0,n.useState)((()=>ts(e,null==r?void 0:r.document)));if(!o){const t=ts(e);t&&a(t)}return(0,n.useEffect)((()=>{t&&o&&t(o)}),[t,o]),(0,n.useEffect)((()=>{const t=ts(e);t!==o&&a(t)}),[e,o]),o}const rs=function({children:e,in:t,onExited:r,mountOnEnter:o,unmountOnExit:a}){const s=(0,n.useRef)(null),i=(0,n.useRef)(t),l=ke(r);(0,n.useEffect)((()=>{t?i.current=!0:l(s.current)}),[t,l]);const c=ne(s,e.ref),u=(0,n.cloneElement)(e,{ref:c});return t?u:a||!i.current&&o?null:u};function os({children:e,in:t,onExited:r,onEntered:o,transition:a}){const[s,i]=(0,n.useState)(!t);t&&s&&i(!1);const l=function({in:e,onTransition:t}){const r=(0,n.useRef)(null),o=(0,n.useRef)(!0),a=ke(t);return Le((()=>{if(!r.current)return;let t=!1;return a({in:e,element:r.current,initial:o.current,isStale:()=>t}),()=>{t=!0}}),[e,a]),Le((()=>(o.current=!1,()=>{o.current=!0})),[]),r}({in:!!t,onTransition:e=>{Promise.resolve(a(e)).then((()=>{e.isStale()||(e.in?null==o||o(e.element,e.initial):(i(!0),null==r||r(e.element)))}),(t=>{throw e.in||i(!0),t}))}}),c=ne(l,e.ref);return s&&!t?null:(0,n.cloneElement)(e,{ref:c})}function as(e,t,n){return e?(0,m.jsx)(e,Object.assign({},n)):t?(0,m.jsx)(os,Object.assign({},n,{transition:t})):(0,m.jsx)(rs,Object.assign({},n))}function ss(e){return"Escape"===e.code||27===e.keyCode}const is=["show","role","className","style","children","backdrop","keyboard","onBackdropClick","onEscapeKeyDown","transition","runTransition","backdropTransition","runBackdropTransition","autoFocus","enforceFocus","restoreFocus","restoreFocusOptions","renderDialog","renderBackdrop","manager","container","onShow","onHide","onExit","onExited","onExiting","onEnter","onEntering","onEntered"];let ls;const cs=(0,n.forwardRef)(((e,t)=>{let{show:r=!1,role:o="dialog",className:a,style:s,children:i,backdrop:l=!0,keyboard:c=!0,onBackdropClick:u,onEscapeKeyDown:d,transition:f,runTransition:p,backdropTransition:v,runBackdropTransition:h,autoFocus:b=!0,enforceFocus:x=!0,restoreFocus:g=!0,restoreFocusOptions:y,renderDialog:w,renderBackdrop:N=(e=>(0,m.jsx)("div",Object.assign({},e))),manager:E,container:j,onShow:C,onHide:O=(()=>{}),onExit:R,onExited:k,onExiting:P,onEnter:T,onEntering:S,onEntered:$}=e,M=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,is);const I=vo(),A=ns(j),L=function(e){const t=vo(),r=e||function(e){return ls||(ls=new es({ownerDocument:null==e?void 0:e.document})),ls}(t),o=(0,n.useRef)({dialog:null,backdrop:null});return Object.assign(o.current,{add:()=>r.add(o.current),remove:()=>r.remove(o.current),isTopModal:()=>r.isTopModal(o.current),setDialogRef:(0,n.useCallback)((e=>{o.current.dialog=e}),[]),setBackdropRef:(0,n.useCallback)((e=>{o.current.backdrop=e}),[])})}(E),B=Me(),F=Ie(r),[_,H]=(0,n.useState)(!r),K=(0,n.useRef)(null);(0,n.useImperativeHandle)(t,(()=>L),[L]),W&&!F&&r&&(K.current=Ja(null==I?void 0:I.document)),r&&_&&H(!1);const V=ke((()=>{if(L.add(),Z.current=X(document,"keydown",G),Y.current=X(document,"focus",(()=>setTimeout(U)),!0),C&&C(),b){var e,t;const n=Ja(null!=(e=null==(t=L.dialog)?void 0:t.ownerDocument)?e:null==I?void 0:I.document);L.dialog&&n&&!$r(L.dialog,n)&&(K.current=n,L.dialog.focus())}})),z=ke((()=>{var e;L.remove(),null==Z.current||Z.current(),null==Y.current||Y.current(),g&&(null==(e=K.current)||null==e.focus||e.focus(y),K.current=null)}));(0,n.useEffect)((()=>{r&&A&&V()}),[r,A,V]),(0,n.useEffect)((()=>{_&&z()}),[_,z]),Kt((()=>{z()}));const U=ke((()=>{if(!x||!B()||!L.isTopModal())return;const e=Ja(null==I?void 0:I.document);L.dialog&&e&&!$r(L.dialog,e)&&L.dialog.focus()})),q=ke((e=>{e.target===e.currentTarget&&(null==u||u(e),!0===l&&O())})),G=ke((e=>{c&&ss(e)&&L.isTopModal()&&(null==d||d(e),e.defaultPrevented||O())})),Y=(0,n.useRef)(),Z=(0,n.useRef)();if(!A)return null;const J=Object.assign({role:o,ref:L.setDialogRef,"aria-modal":"dialog"===o||void 0},M,{style:s,className:a,tabIndex:-1});let Q=w?w(J):(0,m.jsx)("div",Object.assign({},J,{children:n.cloneElement(i,{role:"document"})}));Q=as(f,p,{unmountOnExit:!0,mountOnEnter:!0,appear:!0,in:!!r,onExit:R,onExiting:P,onExited:(...e)=>{H(!0),null==k||k(...e)},onEnter:T,onEntering:S,onEntered:$,children:Q});let ee=null;return l&&(ee=N({ref:L.setBackdropRef,onClick:q}),ee=as(v,h,{in:!!r,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:ee})),(0,m.jsx)(m.Fragment,{children:D().createPortal((0,m.jsxs)(m.Fragment,{children:[ee,Q]}),A)})}));cs.displayName="Modal";const us=Object.assign(cs,{Manager:es});function ds(e,t){return e.classList?!!t&&e.classList.contains(t):-1!==(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")}function fs(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}const ps=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",ms=".sticky-top",vs=".navbar-toggler";class hs extends es{adjustAndStore(e,t,n){const r=t.style[e];t.dataset[e]=r,T(t,{[e]:`${parseFloat(T(t,e))+n}px`})}restore(e,t){const n=t.dataset[e];void 0!==n&&(delete t.dataset[e],T(t,{[e]:n}))}setContainerStyle(e){super.setContainerStyle(e);const t=this.getElement();var n,r;if(r="modal-open",(n=t).classList?n.classList.add(r):ds(n,r)||("string"==typeof n.className?n.className=n.className+" "+r:n.setAttribute("class",(n.className&&n.className.baseVal||"")+" "+r)),!e.scrollBarWidth)return;const o=this.isRTL?"paddingLeft":"paddingRight",a=this.isRTL?"marginLeft":"marginRight";sn(t,ps).forEach((t=>this.adjustAndStore(o,t,e.scrollBarWidth))),sn(t,ms).forEach((t=>this.adjustAndStore(a,t,-e.scrollBarWidth))),sn(t,vs).forEach((t=>this.adjustAndStore(a,t,e.scrollBarWidth)))}removeContainerStyle(e){super.removeContainerStyle(e);const t=this.getElement();var n,r;r="modal-open",(n=t).classList?n.classList.remove(r):"string"==typeof n.className?n.className=fs(n.className,r):n.setAttribute("class",fs(n.className&&n.className.baseVal||"",r));const o=this.isRTL?"paddingLeft":"paddingRight",a=this.isRTL?"marginLeft":"marginRight";sn(t,ps).forEach((e=>this.restore(o,e))),sn(t,ms).forEach((e=>this.restore(a,e))),sn(t,vs).forEach((e=>this.restore(a,e)))}}let bs;function xs(e){return bs||(bs=new hs(e)),bs}const gs=hs,ys=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"modal-body"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));ys.displayName="ModalBody";const ws=ys,Ns=n.createContext({onHide(){}}),Es=n.forwardRef((({bsPrefix:e,className:n,contentClassName:r,centered:o,size:a,fullscreen:s,children:i,scrollable:l,...c},u)=>{const d=`${e=y(e,"modal")}-dialog`,f="string"==typeof s?`${e}-fullscreen-${s}`:`${e}-fullscreen`;return(0,m.jsx)("div",{...c,ref:u,className:t()(d,n,a&&`${e}-${a}`,o&&`${d}-centered`,l&&`${d}-scrollable`,s&&f),children:(0,m.jsx)("div",{className:t()(`${e}-content`,r),children:i})})}));Es.displayName="ModalDialog";const js=Es,Cs=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"modal-footer"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Cs.displayName="ModalFooter";const Os=Cs,Rs=n.forwardRef((({closeLabel:e="Close",closeVariant:t,closeButton:r=!1,onHide:o,children:a,...s},i)=>{const l=(0,n.useContext)(Ns),c=ke((()=>{null==l||l.onHide(),null==o||o()}));return(0,m.jsxs)("div",{ref:i,...s,children:[a,r&&(0,m.jsx)(et,{"aria-label":e,variant:t,onClick:c})]})})),ks=Rs,Ps=n.forwardRef((({bsPrefix:e,className:n,closeLabel:r="Close",closeButton:o=!1,...a},s)=>(e=y(e,"modal-header"),(0,m.jsx)(ks,{ref:s,...a,className:t()(n,e),closeLabel:r,closeButton:o}))));Ps.displayName="ModalHeader";const Ts=Ps,Ss=Pe("h4"),$s=n.forwardRef((({className:e,bsPrefix:n,as:r=Ss,...o},a)=>(n=y(n,"modal-title"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));$s.displayName="ModalTitle";const Ds=$s;function Ms(e){return(0,m.jsx)(Xe,{...e,timeout:null})}function Is(e){return(0,m.jsx)(Xe,{...e,timeout:null})}const As=n.forwardRef((({bsPrefix:e,className:r,style:o,dialogClassName:a,contentClassName:s,children:i,dialogAs:l=js,"data-bs-theme":c,"aria-labelledby":u,"aria-describedby":d,"aria-label":f,show:p=!1,animation:v=!0,backdrop:h=!0,keyboard:b=!0,onEscapeKeyDown:x,onShow:g,onHide:w,container:N,autoFocus:j=!0,enforceFocus:O=!0,restoreFocus:R=!0,restoreFocusOptions:k,onEntered:P,onExit:T,onExiting:S,onEnter:$,onEntering:D,onExited:M,backdropClassName:I,manager:A,...L},B)=>{const[F,_]=(0,n.useState)({}),[H,K]=(0,n.useState)(!1),V=(0,n.useRef)(!1),z=(0,n.useRef)(!1),U=(0,n.useRef)(null),[X,Z]=De(),J=ne(B,Z),Q=ke(w),ee=E();e=y(e,"modal");const te=(0,n.useMemo)((()=>({onHide:Q})),[Q]);function re(){return A||xs({isRTL:ee})}function oe(e){if(!W)return;const t=re().getScrollbarWidth()>0,n=e.scrollHeight>C(e).documentElement.clientHeight;_({paddingRight:t&&!n?Za():void 0,paddingLeft:!t&&n?Za():void 0})}const ae=ke((()=>{X&&oe(X.dialog)}));Kt((()=>{G(window,"resize",ae),null==U.current||U.current()}));const se=()=>{V.current=!0},ie=e=>{V.current&&X&&e.target===X.dialog&&(z.current=!0),V.current=!1},le=()=>{K(!0),U.current=Y(X.dialog,(()=>{K(!1)}))},ce=e=>{"static"!==h?z.current||e.target!==e.currentTarget?z.current=!1:null==w||w():(e=>{e.target===e.currentTarget&&le()})(e)},ue=(0,n.useCallback)((n=>(0,m.jsx)("div",{...n,className:t()(`${e}-backdrop`,I,!v&&"show")})),[v,I,e]),de={...o,...F};return de.display="block",(0,m.jsx)(Ns.Provider,{value:te,children:(0,m.jsx)(us,{show:p,ref:J,backdrop:h,container:N,keyboard:!0,autoFocus:j,enforceFocus:O,restoreFocus:R,restoreFocusOptions:k,onEscapeKeyDown:e=>{b?null==x||x(e):(e.preventDefault(),"static"===h&&le())},onShow:g,onHide:w,onEnter:(e,t)=>{e&&oe(e),null==$||$(e,t)},onEntering:(e,t)=>{null==D||D(e,t),q(window,"resize",ae)},onEntered:P,onExit:e=>{null==U.current||U.current(),null==T||T(e)},onExiting:S,onExited:e=>{e&&(e.style.display=""),null==M||M(e),G(window,"resize",ae)},manager:re(),transition:v?Ms:void 0,backdropTransition:v?Is:void 0,renderBackdrop:ue,renderDialog:n=>(0,m.jsx)("div",{role:"dialog",...n,style:de,className:t()(r,e,H&&`${e}-static`,!v&&"show"),onClick:h?ce:void 0,onMouseUp:ie,"data-bs-theme":c,"aria-label":f,"aria-labelledby":u,"aria-describedby":d,children:(0,m.jsx)(l,{...L,onMouseDown:se,className:a,contentClassName:s,children:i})})})})}));As.displayName="Modal";const Ls=Object.assign(As,{Body:ws,Header:Ts,Title:Ds,Footer:Os,Dialog:js,TRANSITION_DURATION:300,BACKDROP_TRANSITION_DURATION:150});o(946);const Bs=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"nav-item"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Bs.displayName="NavItem";const Fs=Bs,_s=n.forwardRef((({bsPrefix:e,className:n,as:r=Ve,active:o,eventKey:a,disabled:s=!1,...i},l)=>{e=y(e,"nav-link");const[c,u]=Ba({key:ro(a,i.href),active:o,disabled:s,...i});return(0,m.jsx)(r,{...i,...c,ref:l,disabled:s,className:t()(n,e,s&&"disabled",u.isActive&&"active")})}));_s.displayName="NavLink";const Hs=_s,Ks=n.forwardRef(((e,r)=>{const{as:o="div",bsPrefix:a,variant:s,fill:i=!1,justify:l=!1,navbar:c,navbarScroll:u,className:d,activeKey:f,...v}=p(e,{activeKey:"onSelect"}),h=y(a,"nav");let b,x,g=!1;const w=(0,n.useContext)($o),N=(0,n.useContext)(wt);return w?(b=w.bsPrefix,g=null==c||c):N&&({cardHeaderBsPrefix:x}=N),(0,m.jsx)(za,{as:o,ref:r,activeKey:f,className:t()(d,{[h]:!g,[`${b}-nav`]:g,[`${b}-nav-scroll`]:g&&u,[`${x}-${s}`]:!!x,[`${h}-${s}`]:!!s,[`${h}-fill`]:i,[`${h}-justified`]:l}),...v})}));Ks.displayName="Nav";const Ws=Object.assign(Ks,{Item:Fs,Link:Hs}),Vs=n.forwardRef((({bsPrefix:e,className:n,as:r,...o},a)=>{e=y(e,"navbar-brand");const s=r||(o.href?"a":"span");return(0,m.jsx)(s,{...o,ref:a,className:t()(n,e)})}));Vs.displayName="NavbarBrand";const zs=Vs,Us=n.forwardRef((({children:e,bsPrefix:t,...r},o)=>{t=y(t,"navbar-collapse");const a=(0,n.useContext)($o);return(0,m.jsx)(le,{in:!(!a||!a.expanded),...r,children:(0,m.jsx)("div",{ref:o,className:t,children:e})})}));Us.displayName="NavbarCollapse";const qs=Us,Gs=n.forwardRef((({bsPrefix:e,className:r,children:o,label:a="Toggle navigation",as:s="button",onClick:i,...l},c)=>{e=y(e,"navbar-toggler");const{onToggle:u,expanded:d}=(0,n.useContext)($o)||{},f=ke((e=>{i&&i(e),u&&u()}));return"button"===s&&(l.type="button"),(0,m.jsx)(s,{...l,ref:c,onClick:f,"aria-label":a,className:t()(r,e,!d&&"collapsed"),children:o||(0,m.jsx)("span",{className:`${e}-icon`})})}));Gs.displayName="NavbarToggle";const Xs=Gs;var Ys=new WeakMap,Zs=function(e,t){if(e&&t){var n=Ys.get(t)||new Map;Ys.set(t,n);var r=n.get(e);return r||((r=t.matchMedia(e)).refCount=0,n.set(r.media,r)),r}};function Js(e,t){void 0===t&&(t="undefined"==typeof window?void 0:window);var r=Zs(e,t),o=(0,n.useState)((function(){return!!r&&r.matches})),a=o[0],s=o[1];return Le((function(){var n=Zs(e,t);if(!n)return s(!1);var r=Ys.get(t),o=function(){s(n.matches)};return n.refCount++,n.addListener(o),o(),function(){n.removeListener(o),n.refCount--,n.refCount<=0&&(null==r||r.delete(n.media)),n=void 0}}),[e]),a}const Qs=function(e){var t=Object.keys(e);function r(e,t){return e===t?t:e?e+" and "+t:t}return function(o,a,s){var i,l;return"object"==typeof o?(i=o,s=a,a=!0):((l={})[o]=a=a||!0,i=l),Js((0,n.useMemo)((function(){return Object.entries(i).reduce((function(n,o){var a,s=o[0],i=o[1];return"up"!==i&&!0!==i||(n=r(n,("number"==typeof(a=e[s])&&(a+="px"),"(min-width: "+a+")"))),"down"!==i&&!0!==i||(n=r(n,function(n){var r=function(e){return t[Math.min(t.indexOf(e)+1,t.length-1)]}(n),o=e[r];return"(max-width: "+(o="number"==typeof o?o-.2+"px":"calc("+o+" - 0.2px)")+")"}(s))),n}),"")}),[JSON.stringify(i)]),s)}}({xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400}),ei=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"offcanvas-body"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));ei.displayName="OffcanvasBody";const ti=ei,ni={[L]:"show",[B]:"show"},ri=n.forwardRef((({bsPrefix:e,className:r,children:o,in:a=!1,mountOnEnter:s=!1,unmountOnExit:i=!1,appear:l=!1,...c},u)=>(e=y(e,"offcanvas"),(0,m.jsx)(oe,{ref:u,addEndListener:J,in:a,mountOnEnter:s,unmountOnExit:i,appear:l,...c,childRef:o.ref,children:(a,s)=>n.cloneElement(o,{...s,className:t()(r,o.props.className,(a===L||a===F)&&`${e}-toggling`,ni[a])})}))));ri.displayName="OffcanvasToggling";const oi=ri,ai=n.forwardRef((({bsPrefix:e,className:n,closeLabel:r="Close",closeButton:o=!1,...a},s)=>(e=y(e,"offcanvas-header"),(0,m.jsx)(ks,{ref:s,...a,className:t()(n,e),closeLabel:r,closeButton:o}))));ai.displayName="OffcanvasHeader";const si=ai,ii=Pe("h5"),li=n.forwardRef((({className:e,bsPrefix:n,as:r=ii,...o},a)=>(n=y(n,"offcanvas-title"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));li.displayName="OffcanvasTitle";const ci=li;function ui(e){return(0,m.jsx)(oi,{...e})}function di(e){return(0,m.jsx)(Xe,{...e})}const fi=n.forwardRef((({bsPrefix:e,className:r,children:o,"aria-labelledby":a,placement:s="start",responsive:i,show:l=!1,backdrop:c=!0,keyboard:u=!0,scroll:d=!1,onEscapeKeyDown:f,onShow:p,onHide:v,container:h,autoFocus:b=!0,enforceFocus:x=!0,restoreFocus:g=!0,restoreFocusOptions:w,onEntered:N,onExit:E,onExiting:j,onEnter:C,onEntering:O,onExited:R,backdropClassName:k,manager:P,renderStaticNode:T=!1,...S},$)=>{const D=(0,n.useRef)();e=y(e,"offcanvas");const{onToggle:M}=(0,n.useContext)($o)||{},[I,A]=(0,n.useState)(!1),L=Qs(i||"xs","up");(0,n.useEffect)((()=>{A(i?l&&!L:l)}),[l,i,L]);const B=ke((()=>{null==M||M(),null==v||v()})),F=(0,n.useMemo)((()=>({onHide:B})),[B]),_=(0,n.useCallback)((n=>(0,m.jsx)("div",{...n,className:t()(`${e}-backdrop`,k)})),[k,e]),H=n=>(0,m.jsx)("div",{...n,...S,className:t()(r,i?`${e}-${i}`:e,`${e}-${s}`),"aria-labelledby":a,children:o});return(0,m.jsxs)(m.Fragment,{children:[!I&&(i||T)&&H({}),(0,m.jsx)(Ns.Provider,{value:F,children:(0,m.jsx)(us,{show:I,ref:$,backdrop:c,container:h,keyboard:u,autoFocus:b,enforceFocus:x&&!d,restoreFocus:g,restoreFocusOptions:w,onEscapeKeyDown:f,onShow:p,onHide:B,onEnter:(e,...t)=>{e&&(e.style.visibility="visible"),null==C||C(e,...t)},onEntering:O,onEntered:N,onExit:E,onExiting:j,onExited:(e,...t)=>{e&&(e.style.visibility=""),null==R||R(...t)},manager:P||(d?(D.current||(D.current=new gs({handleContainerOverflow:!1})),D.current):xs()),transition:ui,backdropTransition:di,renderBackdrop:_,renderDialog:H})})]})}));fi.displayName="Offcanvas";const pi=Object.assign(fi,{Body:ti,Header:si,Title:ci}),mi=n.forwardRef(((e,t)=>{const r=(0,n.useContext)($o);return(0,m.jsx)(pi,{ref:t,show:!(null==r||!r.expanded),...e,renderStaticNode:!0})}));mi.displayName="NavbarOffcanvas";const vi=mi,hi=n.forwardRef((({className:e,bsPrefix:n,as:r="span",...o},a)=>(n=y(n,"navbar-text"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));hi.displayName="NavbarText";const bi=hi,xi=n.forwardRef(((e,r)=>{const{bsPrefix:o,expand:a=!0,variant:s="light",bg:i,fixed:l,sticky:c,className:u,as:d="nav",expanded:f,onToggle:v,onSelect:h,collapseOnSelect:b=!1,...x}=p(e,{expanded:"onToggle"}),g=y(o,"navbar"),w=(0,n.useCallback)(((...e)=>{null==h||h(...e),b&&f&&(null==v||v(!1))}),[h,b,f,v]);void 0===x.role&&"nav"!==d&&(x.role="navigation");let N=`${g}-expand`;"string"==typeof a&&(N=`${N}-${a}`);const E=(0,n.useMemo)((()=>({onToggle:()=>null==v?void 0:v(!f),bsPrefix:g,expanded:!!f,expand:a})),[g,f,a,v]);return(0,m.jsx)($o.Provider,{value:E,children:(0,m.jsx)(oo.Provider,{value:w,children:(0,m.jsx)(d,{ref:r,...x,className:t()(u,g,a&&N,s&&`${g}-${s}`,i&&`bg-${i}`,c&&`sticky-${c}`,l&&`fixed-${l}`)})})})}));xi.displayName="Navbar";const gi=Object.assign(xi,{Brand:zs,Collapse:qs,Offcanvas:vi,Text:bi,Toggle:Xs}),yi=n.forwardRef((({id:e,title:n,children:r,bsPrefix:o,className:a,rootCloseEvent:s,menuRole:i,disabled:l,active:c,renderMenuOnMount:u,menuVariant:d,...f},p)=>{const v=y(void 0,"nav-item");return(0,m.jsxs)(_o,{ref:p,...f,className:t()(a,v),children:[(0,m.jsx)(_o.Toggle,{id:e,eventKey:null,active:c,disabled:l,childBsPrefix:o,as:Hs,children:n}),(0,m.jsx)(_o.Menu,{role:i,renderOnMount:u,rootCloseEvent:s,variant:d,children:r})]})}));yi.displayName="NavDropdown";const wi=Object.assign(yi,{Item:_o.Item,ItemText:_o.ItemText,Divider:_o.Divider,Header:_o.Header}),Ni=()=>{},Ei=n.forwardRef(((e,t)=>{const{flip:r,offset:o,placement:a,containerPadding:s,popperConfig:i={},transition:l,runTransition:c}=e,[u,d]=De(),[f,p]=De(),m=ne(d,t),v=ns(e.container),h=ns(e.target),[b,x]=(0,n.useState)(!e.show),g=Sr(h,u,_r({placement:a,enableEvents:!!e.show,containerPadding:s||5,flip:r,offset:o,arrowElement:f,popperConfig:i}));e.show&&b&&x(!1);const y=e.show||!b;if(function(e,t,{disabled:r,clickTrigger:o}={}){const a=t||Ni;Br(e,a,{disabled:r,clickTrigger:o});const s=ke((e=>{ss(e)&&a(e)}));(0,n.useEffect)((()=>{if(r||null==e)return;const t=C(Ar(e));let n=(t.defaultView||window).event;const o=X(t,"keyup",(e=>{e!==n?s(e):n=void 0}));return()=>{o()}}),[e,r,s])}(u,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!y)return null;const{onExit:w,onExiting:N,onEnter:E,onEntering:j,onEntered:O}=e;let R=e.children(Object.assign({},g.attributes.popper,{style:g.styles.popper,ref:m}),{popper:g,placement:a,show:!!e.show,arrowProps:Object.assign({},g.attributes.arrow,{style:g.styles.arrow,ref:p})});return R=as(l,c,{in:!!e.show,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:R,onExit:w,onExiting:N,onExited:(...t)=>{x(!0),e.onExited&&e.onExited(...t)},onEnter:E,onEntering:j,onEntered:O}),v?D().createPortal(R,v):null}));Ei.displayName="Overlay";const ji=Ei,Ci=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"popover-header"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ci.displayName="PopoverHeader";const Oi=Ci,Ri=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"popover-body"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Ri.displayName="PopoverBody";const ki=Ri;class Pi extends n.Component{}function Ti(e,t){let n=e;return"left"===e?n=t?"end":"start":"right"===e&&(n=t?"start":"end"),n}function Si(e="absolute"){return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}}const $i=n.forwardRef((({bsPrefix:e,placement:n="right",className:r,style:o,children:a,body:s,arrowProps:i,hasDoneInitialMeasure:l,popper:c,show:u,...d},f)=>{const p=y(e,"popover"),v=E(),[h]=(null==n?void 0:n.split("-"))||[],b=Ti(h,v);let x=o;return u&&!l&&(x={...o,...Si(null==c?void 0:c.strategy)}),(0,m.jsxs)("div",{ref:f,role:"tooltip",style:x,"x-placement":h,className:t()(r,p,h&&`bs-popover-${b}`),...d,children:[(0,m.jsx)("div",{className:"popover-arrow",...i}),s?(0,m.jsx)(ki,{children:a}):a]})})),Di=Object.assign($i,{Header:Oi,Body:ki,POPPER_OFFSET:[0,8]}),Mi=n.forwardRef((({bsPrefix:e,placement:n="right",className:r,style:o,children:a,arrowProps:s,hasDoneInitialMeasure:i,popper:l,show:c,...u},d)=>{e=y(e,"tooltip");const f=E(),[p]=(null==n?void 0:n.split("-"))||[],v=Ti(p,f);let h=o;return c&&!i&&(h={...o,...Si(null==l?void 0:l.strategy)}),(0,m.jsxs)("div",{ref:d,style:h,role:"tooltip","x-placement":p,className:t()(r,e,`bs-tooltip-${v}`),...u,children:[(0,m.jsx)("div",{className:"tooltip-arrow",...s}),(0,m.jsx)("div",{className:`${e}-inner`,children:a})]})}));Mi.displayName="Tooltip";const Ii=Object.assign(Mi,{TOOLTIP_OFFSET:[0,6]}),Ai=n.forwardRef((({children:e,transition:r=Xe,popperConfig:o={},rootClose:a=!1,placement:s="top",show:i=!1,...l},c)=>{const u=(0,n.useRef)({}),[d,f]=(0,n.useState)(null),[p,v]=function(e){const t=(0,n.useRef)(null),r=y(void 0,"popover"),o=y(void 0,"tooltip"),a=(0,n.useMemo)((()=>({name:"offset",options:{offset:()=>{if(e)return e;if(t.current){if(ds(t.current,r))return Di.POPPER_OFFSET;if(ds(t.current,o))return Ii.TOOLTIP_OFFSET}return[0,0]}}})),[e,r,o]);return[t,[a]]}(l.offset),h=ne(c,p),b=!0===r?Xe:r||void 0,x=ke((e=>{f(e),null==o||null==o.onFirstUpdate||o.onFirstUpdate(e)}));return Le((()=>{d&&l.target&&(null==u.current.scheduleUpdate||u.current.scheduleUpdate())}),[d,l.target]),(0,n.useEffect)((()=>{i||f(null)}),[i]),(0,m.jsx)(ji,{...l,ref:h,popperConfig:{...o,modifiers:v.concat(o.modifiers||[]),onFirstUpdate:x},transition:b,rootClose:a,placement:s,show:i,children:(a,{arrowProps:s,popper:i,show:l})=>{var c,f;!function(e,t){const{ref:n}=e,{ref:r}=t;e.ref=n.__wrapped||(n.__wrapped=e=>n(re(e))),t.ref=r.__wrapped||(r.__wrapped=e=>r(re(e)))}(a,s);const p=null==i?void 0:i.placement,m=Object.assign(u.current,{state:null==i?void 0:i.state,scheduleUpdate:null==i?void 0:i.update,placement:p,outOfBoundaries:(null==i||null==(c=i.state)||null==(f=c.modifiersData.hide)?void 0:f.isReferenceHidden)||!1,strategy:o.strategy}),v=!!d;return"function"==typeof e?e({...a,placement:p,show:l,...!r&&l&&{className:"show"},popper:m,arrowProps:s,hasDoneInitialMeasure:v}):n.cloneElement(e,{...a,placement:p,arrowProps:s,popper:m,hasDoneInitialMeasure:v,className:t()(e.props.className,!r&&l&&"show"),style:{...e.props.style,...a.style}})}})}));Ai.displayName="Overlay";const Li=Ai;function Bi(e,t,n){const[r]=t,o=r.currentTarget,a=r.relatedTarget||r.nativeEvent[n];a&&a===o||$r(o,a)||e(...t)}Ze().oneOf(["click","hover","focus"]);const Fi=({trigger:e=["hover","focus"],overlay:t,children:r,popperConfig:o={},show:a,defaultShow:s=!1,onToggle:i,delay:l,placement:c,flip:u=c&&-1!==c.indexOf("auto"),...d})=>{const p=(0,n.useRef)(null),v=ne(p,r.ref),h=zt(),b=(0,n.useRef)(""),[x,g]=f(a,s,i),y=function(e){return e&&"object"==typeof e?e:{show:e,hide:e}}(l),{onFocus:w,onBlur:N,onClick:E}="function"!=typeof r?n.Children.only(r).props:{},j=(0,n.useCallback)((()=>{h.clear(),b.current="show",y.show?h.set((()=>{"show"===b.current&&g(!0)}),y.show):g(!0)}),[y.show,g,h]),C=(0,n.useCallback)((()=>{h.clear(),b.current="hide",y.hide?h.set((()=>{"hide"===b.current&&g(!1)}),y.hide):g(!1)}),[y.hide,g,h]),O=(0,n.useCallback)(((...e)=>{j(),null==w||w(...e)}),[j,w]),R=(0,n.useCallback)(((...e)=>{C(),null==N||N(...e)}),[C,N]),k=(0,n.useCallback)(((...e)=>{g(!x),null==E||E(...e)}),[E,g,x]),P=(0,n.useCallback)(((...e)=>{Bi(j,e,"fromElement")}),[j]),T=(0,n.useCallback)(((...e)=>{Bi(C,e,"toElement")}),[C]),S=null==e?[]:[].concat(e),$={ref:e=>{v(re(e))}};return-1!==S.indexOf("click")&&($.onClick=k),-1!==S.indexOf("focus")&&($.onFocus=O,$.onBlur=R),-1!==S.indexOf("hover")&&($.onMouseOver=P,$.onMouseOut=T),(0,m.jsxs)(m.Fragment,{children:["function"==typeof r?r($):(0,n.cloneElement)(r,$),(0,m.jsx)(Li,{...d,show:x,onHide:C,flip:u,placement:c,popperConfig:o,target:p.current,children:t})]})},_i=n.forwardRef((({active:e=!1,disabled:n=!1,className:r,style:o,activeLabel:a="(current)",children:s,linkStyle:i,linkClassName:l,as:c=Ve,...u},d)=>{const f=e||n?"span":c;return(0,m.jsx)("li",{ref:d,style:o,className:t()(r,"page-item",{active:e,disabled:n}),children:(0,m.jsxs)(f,{className:t()("page-link",l),style:i,...u,children:[s,e&&a&&(0,m.jsx)("span",{className:"visually-hidden",children:a})]})})}));_i.displayName="PageItem";const Hi=_i;function Ki(e,t,r=e){const o=n.forwardRef((({children:e,...n},o)=>(0,m.jsxs)(_i,{...n,ref:o,children:[(0,m.jsx)("span",{"aria-hidden":"true",children:e||t}),(0,m.jsx)("span",{className:"visually-hidden",children:r})]})));return o.displayName=e,o}const Wi=Ki("First","«"),Vi=Ki("Prev","‹","Previous"),zi=Ki("Ellipsis","…","More"),Ui=Ki("Next","›"),qi=Ki("Last","»"),Gi=n.forwardRef((({bsPrefix:e,className:n,size:r,...o},a)=>{const s=y(e,"pagination");return(0,m.jsx)("ul",{ref:a,...o,className:t()(n,s,r&&`${s}-${r}`)})}));Gi.displayName="Pagination";const Xi=Object.assign(Gi,{First:Wi,Prev:Vi,Ellipsis:zi,Item:Hi,Next:Ui,Last:qi});function Yi({animation:e,bg:n,bsPrefix:r,size:o,...a}){r=y(r,"placeholder");const[{className:s,...i}]=en(a);return{...i,className:t()(s,e?`${r}-${e}`:r,o&&`${r}-${o}`,n&&`bg-${n}`)}}const Zi=n.forwardRef(((e,t)=>{const n=Yi(e);return(0,m.jsx)(dt,{...n,ref:t,disabled:!0,tabIndex:-1})}));Zi.displayName="PlaceholderButton";const Ji=Zi,Qi=n.forwardRef((({as:e="span",...t},n)=>{const r=Yi(t);return(0,m.jsx)(e,{...r,ref:n})}));Qi.displayName="Placeholder";const el=Object.assign(Qi,{Button:Ji}),tl=1e3;function nl(e,t,n){const r=(e-t)/(n-t)*100;return Math.round(r*tl)/tl}function rl({min:e,now:n,max:r,label:o,visuallyHidden:a,striped:s,animated:i,className:l,style:c,variant:u,bsPrefix:d,...f},p){return(0,m.jsx)("div",{ref:p,...f,role:"progressbar",className:t()(l,`${d}-bar`,{[`bg-${u}`]:u,[`${d}-bar-animated`]:i,[`${d}-bar-striped`]:i||s}),style:{width:`${nl(n,e,r)}%`,...c},"aria-valuenow":n,"aria-valuemin":e,"aria-valuemax":r,children:a?(0,m.jsx)("span",{className:"visually-hidden",children:o}):o})}const ol=n.forwardRef((({isChild:e=!1,...r},o)=>{const a={min:0,max:100,animated:!1,visuallyHidden:!1,striped:!1,...r};if(a.bsPrefix=y(a.bsPrefix,"progress"),e)return rl(a,o);const{min:s,now:i,max:l,label:c,visuallyHidden:u,striped:d,animated:f,bsPrefix:p,variant:v,className:h,children:b,...x}=a;return(0,m.jsx)("div",{ref:o,...x,className:t()(h,p),children:b?Yt(b,(e=>(0,n.cloneElement)(e,{isChild:!0}))):rl({min:s,now:i,max:l,label:c,visuallyHidden:u,striped:d,animated:f,bsPrefix:p,variant:v},o)})}));ol.displayName="ProgressBar";const al=ol,sl=n.forwardRef((({bsPrefix:e,className:r,children:o,aspectRatio:a="1x1",style:s,...i},l)=>{e=y(e,"ratio");const c="number"==typeof a;return(0,m.jsx)("div",{ref:l,...i,style:{...s,...c&&{"--bs-aspect-ratio":(u=a,u<=0?"100%":u<1?100*u+"%":`${u}%`)}},className:t()(e,r,!c&&`${e}-${a}`),children:n.Children.only(o)});var u})),il=n.forwardRef((({bsPrefix:e,className:n,as:r="div",...o},a)=>{const s=y(e,"row"),i=w(),l=N(),c=`${s}-cols`,u=[];return i.forEach((e=>{const t=o[e];let n;delete o[e],null!=t&&"object"==typeof t?({cols:n}=t):n=t;const r=e!==l?`-${e}`:"";null!=n&&u.push(`${c}${r}-${n}`)})),(0,m.jsx)(r,{ref:a,...o,className:t()(n,s,...u)})}));il.displayName="Row";const ll=il,cl=n.forwardRef((({bsPrefix:e,variant:n,animation:r="border",size:o,as:a="div",className:s,...i},l)=>{const c=`${e=y(e,"spinner")}-${r}`;return(0,m.jsx)(a,{ref:l,...i,className:t()(s,c,o&&`${c}-${o}`,n&&`text-${n}`)})}));cl.displayName="Spinner";const ul=cl,dl={id:Ze().string,toggleLabel:Ze().string,href:Ze().string,target:Ze().string,onClick:Ze().func,title:Ze().node.isRequired,type:Ze().string,disabled:Ze().bool,align:Ko,menuRole:Ze().string,renderMenuOnMount:Ze().bool,rootCloseEvent:Ze().string,flip:Ze().bool,bsPrefix:Ze().string,variant:Ze().string,size:Ze().string},fl=n.forwardRef((({id:e,bsPrefix:t,size:n,variant:r,title:o,type:a="button",toggleLabel:s="Toggle dropdown",children:i,onClick:l,href:c,target:u,menuRole:d,renderMenuOnMount:f,rootCloseEvent:p,flip:v,...h},b)=>(0,m.jsxs)(_o,{ref:b,...h,as:pt,children:[(0,m.jsx)(dt,{size:n,variant:r,disabled:h.disabled,bsPrefix:t,href:c,target:u,onClick:l,type:a,children:o}),(0,m.jsx)(_o.Toggle,{split:!0,id:e,size:n,variant:r,disabled:h.disabled,childBsPrefix:t,children:(0,m.jsx)("span",{className:"visually-hidden",children:s})}),(0,m.jsx)(_o.Menu,{role:d,renderOnMount:f,rootCloseEvent:p,flip:v,children:i})]})));fl.propTypes=dl,fl.displayName="SplitButton";const pl=fl,ml=function(e){let t=(0,n.useContext)(qr),r=Yr(t===Ur),[o,a]=(0,n.useState)(!0),s=(0,n.useMemo)((()=>({prefix:t===Ur?"":`${t.prefix}-${r}`,current:0,isSSR:o})),[t,r,o]);return"undefined"!=typeof window&&(0,n.useLayoutEffect)((()=>{a(!1)}),[]),n.createElement(qr.Provider,{value:s},e.children)};function vl(e,t=v,n=h){const r=[];return Object.entries(e).forEach((([e,o])=>{null!=o&&("object"==typeof o?t.forEach((t=>{const a=o[t];if(null!=a){const o=t!==n?`-${t}`:"";r.push(`${e}${o}-${a}`)}})):r.push(`${e}-${o}`))})),r}const hl=n.forwardRef((({as:e="div",bsPrefix:n,className:r,direction:o,gap:a,...s},i)=>{n=y(n,"horizontal"===o?"hstack":"vstack");const l=w(),c=N();return(0,m.jsx)(e,{...s,ref:i,className:t()(r,n,...vl({gap:a},l,c))})}));hl.displayName="Stack";const bl=hl,xl=["active","eventKey","mountOnEnter","transition","unmountOnExit","role","onEnter","onEntering","onEntered","onExit","onExiting","onExited"],gl=["activeKey","getControlledId","getControllerId"],yl=["as"];function wl(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function Nl(e){let{active:t,eventKey:r,mountOnEnter:o,transition:a,unmountOnExit:s,role:i="tabpanel",onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}=e,m=wl(e,xl);const v=(0,n.useContext)(Aa);if(!v)return[Object.assign({},m,{role:i}),{eventKey:r,isActive:t,mountOnEnter:o,transition:a,unmountOnExit:s,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}];const{activeKey:h,getControlledId:b,getControllerId:x}=v,g=wl(v,gl),y=ro(r);return[Object.assign({},m,{role:i,id:b(r),"aria-labelledby":x(r)}),{eventKey:r,isActive:null==t&&null!=y?ro(h)===y:t,transition:a||g.transition,mountOnEnter:null!=o?o:g.mountOnEnter,unmountOnExit:null!=s?s:g.unmountOnExit,onEnter:l,onEntering:c,onEntered:u,onExit:d,onExiting:f,onExited:p}]}const El=n.forwardRef(((e,t)=>{let{as:n="div"}=e,r=wl(e,yl);const[o,{isActive:a,onEnter:s,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:p,transition:v=rs}]=Nl(r);return(0,m.jsx)(Aa.Provider,{value:null,children:(0,m.jsx)(oo.Provider,{value:null,children:(0,m.jsx)(v,{in:a,onEnter:s,onEntering:i,onEntered:l,onExit:c,onExiting:u,onExited:d,mountOnEnter:f,unmountOnExit:p,children:(0,m.jsx)(n,Object.assign({},o,{ref:t,hidden:!a,"aria-hidden":!a}))})})})}));El.displayName="TabPanel";const jl=e=>{const{id:t,generateChildId:r,onSelect:o,activeKey:a,defaultActiveKey:s,transition:i,mountOnEnter:l,unmountOnExit:c,children:u}=e,[d,f]=ln(a,s,o),p=Zr(t),v=(0,n.useMemo)((()=>r||((e,t)=>p?`${p}-${t}-${e}`:null)),[p,r]),h=(0,n.useMemo)((()=>({onSelect:f,activeKey:d,transition:i,mountOnEnter:l||!1,unmountOnExit:c||!1,getControlledId:e=>v(e,"tabpane"),getControllerId:e=>v(e,"tab")})),[f,d,i,l,c,v]);return(0,m.jsx)(Aa.Provider,{value:h,children:(0,m.jsx)(oo.Provider,{value:f||null,children:u})})};jl.Panel=El;const Cl=jl;function Ol(e){return"boolean"==typeof e?e?Xe:rs:e}const Rl=({transition:e,...t})=>(0,m.jsx)(Cl,{...t,transition:Ol(e)});Rl.displayName="TabContainer";const kl=Rl,Pl=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"tab-content"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Pl.displayName="TabContent";const Tl=Pl,Sl=n.forwardRef((({bsPrefix:e,transition:n,...r},o)=>{const[{className:a,as:s="div",...i},{isActive:l,onEnter:c,onEntering:u,onEntered:d,onExit:f,onExiting:p,onExited:v,mountOnEnter:h,unmountOnExit:b,transition:x=Xe}]=Nl({...r,transition:Ol(n)}),g=y(e,"tab-pane");return(0,m.jsx)(Aa.Provider,{value:null,children:(0,m.jsx)(oo.Provider,{value:null,children:(0,m.jsx)(x,{in:l,onEnter:c,onEntering:u,onEntered:d,onExit:f,onExiting:p,onExited:v,mountOnEnter:h,unmountOnExit:b,children:(0,m.jsx)(s,{...i,ref:o,className:t()(a,g,l&&"active")})})})})}));Sl.displayName="TabPane";const $l=Sl,Dl={eventKey:Ze().oneOfType([Ze().string,Ze().number]),title:Ze().node.isRequired,disabled:Ze().bool,tabClassName:Ze().string,tabAttrs:Ze().object},Ml=()=>{throw new Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};Ml.propTypes=Dl;const Il=Object.assign(Ml,{Container:kl,Content:Tl,Pane:$l}),Al=n.forwardRef((({bsPrefix:e,className:n,striped:r,bordered:o,borderless:a,hover:s,size:i,variant:l,responsive:c,...u},d)=>{const f=y(e,"table"),p=t()(n,f,l&&`${f}-${l}`,i&&`${f}-${i}`,r&&`${f}-${"string"==typeof r?`striped-${r}`:"striped"}`,o&&`${f}-bordered`,a&&`${f}-borderless`,s&&`${f}-hover`),v=(0,m.jsx)("table",{...u,className:p,ref:d});if(c){let e=`${f}-responsive`;return"string"==typeof c&&(e=`${e}-${c}`),(0,m.jsx)("div",{className:e,children:v})}return v})),Ll=Al;function Bl(e){let t;return Zt(e,(e=>{null==t&&(t=e.props.eventKey)})),t}function Fl(e){const{title:t,eventKey:n,disabled:r,tabClassName:o,tabAttrs:a,id:s}=e.props;return null==t?null:(0,m.jsx)(Fs,{as:"li",role:"presentation",children:(0,m.jsx)(Hs,{as:"button",type:"button",eventKey:n,disabled:r,id:s,className:o,...a,children:t})})}const _l=e=>{const{id:t,onSelect:n,transition:r,mountOnEnter:o=!1,unmountOnExit:a=!1,variant:s="tabs",children:i,activeKey:l=Bl(i),...c}=p(e,{activeKey:"onSelect"});return(0,m.jsxs)(Cl,{id:t,activeKey:l,onSelect:n,transition:Ol(r),mountOnEnter:o,unmountOnExit:a,children:[(0,m.jsx)(Ws,{...c,role:"tablist",as:"ul",variant:s,children:Yt(i,Fl)}),(0,m.jsx)(Tl,{children:Yt(i,(e=>{const t={...e.props};return delete t.title,delete t.disabled,delete t.tabClassName,delete t.tabAttrs,(0,m.jsx)($l,{...t})}))})]})};_l.displayName="Tabs";const Hl=_l,Kl={[L]:"showing",[F]:"showing show"},Wl=n.forwardRef(((e,t)=>(0,m.jsx)(Xe,{...e,ref:t,transitionClasses:Kl})));Wl.displayName="ToastFade";const Vl=Wl,zl=n.createContext({onClose(){}}),Ul=n.forwardRef((({bsPrefix:e,closeLabel:r="Close",closeVariant:o,closeButton:a=!0,className:s,children:i,...l},c)=>{e=y(e,"toast-header");const u=(0,n.useContext)(zl),d=ke((e=>{null==u||null==u.onClose||u.onClose(e)}));return(0,m.jsxs)("div",{ref:c,...l,className:t()(e,s),children:[i,a&&(0,m.jsx)(et,{"aria-label":r,variant:o,onClick:d,"data-dismiss":"toast"})]})}));Ul.displayName="ToastHeader";const ql=Ul,Gl=n.forwardRef((({className:e,bsPrefix:n,as:r="div",...o},a)=>(n=y(n,"toast-body"),(0,m.jsx)(r,{ref:a,className:t()(e,n),...o}))));Gl.displayName="ToastBody";const Xl=Gl,Yl=n.forwardRef((({bsPrefix:e,className:r,transition:o=Vl,show:a=!0,animation:s=!0,delay:i=5e3,autohide:l=!1,onClose:c,onEntered:u,onExit:d,onExiting:f,onEnter:p,onEntering:v,onExited:h,bg:b,...x},g)=>{e=y(e,"toast");const w=(0,n.useRef)(i),N=(0,n.useRef)(c);(0,n.useEffect)((()=>{w.current=i,N.current=c}),[i,c]);const E=zt(),j=!(!l||!a),C=(0,n.useCallback)((()=>{j&&(null==N.current||N.current())}),[j]);(0,n.useEffect)((()=>{E.set(C,w.current)}),[E,C]);const O=(0,n.useMemo)((()=>({onClose:c})),[c]),R=!(!o||!s),k=(0,m.jsx)("div",{...x,ref:g,className:t()(e,r,b&&`bg-${b}`,!R&&(a?"show":"hide")),role:"alert","aria-live":"assertive","aria-atomic":"true"});return(0,m.jsx)(zl.Provider,{value:O,children:R&&o?(0,m.jsx)(o,{in:a,onEnter:p,onEntering:v,onEntered:u,onExit:d,onExiting:f,onExited:h,unmountOnExit:!0,children:k}):k})}));Yl.displayName="Toast";const Zl=Object.assign(Yl,{Body:Xl,Header:ql}),Jl={"top-start":"top-0 start-0","top-center":"top-0 start-50 translate-middle-x","top-end":"top-0 end-0","middle-start":"top-50 start-0 translate-middle-y","middle-center":"top-50 start-50 translate-middle","middle-end":"top-50 end-0 translate-middle-y","bottom-start":"bottom-0 start-0","bottom-center":"bottom-0 start-50 translate-middle-x","bottom-end":"bottom-0 end-0"},Ql=n.forwardRef((({bsPrefix:e,position:n,containerPosition:r,className:o,as:a="div",...s},i)=>(e=y(e,"toast-container"),(0,m.jsx)(a,{ref:i,...s,className:t()(e,n&&Jl[n],r&&`position-${r}`,o)}))));Ql.displayName="ToastContainer";const ec=Ql,tc=()=>{},nc=n.forwardRef((({bsPrefix:e,name:n,className:r,checked:o,type:a,onChange:s,value:i,disabled:l,id:c,inputRef:u,...d},f)=>(e=y(e,"btn-check"),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("input",{className:e,name:n,type:a,value:i,ref:u,autoComplete:"off",checked:!!o,disabled:!!l,onChange:s||tc,id:c}),(0,m.jsx)(dt,{...d,ref:f,className:t()(r,l&&"disabled"),type:void 0,role:void 0,as:"label",htmlFor:c})]}))));nc.displayName="ToggleButton";const rc=nc,oc=n.forwardRef(((e,t)=>{const{children:r,type:o="radio",name:a,value:s,onChange:i,vertical:l=!1,...u}=p(e,{value:"onChange"}),d=()=>null==s?[]:[].concat(s);return"radio"!==o||a||c()(!1),(0,m.jsx)(pt,{...u,ref:t,vertical:l,children:Yt(r,(e=>{const t=d(),{value:r,onChange:s}=e.props;return n.cloneElement(e,{type:o,name:e.name||a,checked:-1!==t.indexOf(r),onChange:Q(s,(e=>((e,t)=>{if(!i)return;const n=d(),r=-1!==n.indexOf(e);"radio"!==o?i(r?n.filter((t=>t!==e)):[...n,e],t):r||i(e,t)})(r,e)))})}))})})),ac=Object.assign(oc,{Button:rc})})(),a})()));