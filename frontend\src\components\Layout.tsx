import { useAuth } from '@/context/AuthContext';
import <PERSON> from 'next/head';
import Link from 'next/link';
import React, { ReactNode } from 'react';
import { FiLogOut, FiMail, FiSettings, FiUser } from 'react-icons/fi';

interface LayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  title = 'DealClosed Partner',
  description = 'Connect companies with freelance sales professionals'
}) => {
  const { user, logout } = useAuth();

  const getProfileImageUrl = () => {
    if (!user?.profile_image || user.profile_image === 'default.png') {
      return '/images/default-avatar.png';
    }
    const folder = user.role === 'company' ? 'business' : 'sales';
    return `${process.env.NEXT_PUBLIC_API_URL}/assets/img/${folder}/${user.profile_image}`;
  };

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm border-bottom">
        <div className="container">
          <Link href="/" className="navbar-brand fw-bold text-primary text-decoration-none">
            DealClosed Partner
          </Link>

          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav me-auto">
              <li className="nav-item">
                <Link href="/" className="nav-link">Home</Link>
              </li>
              <li className="nav-item">
                <Link href="/jobs" className="nav-link">Jobs</Link>
              </li>
              <li className="nav-item">
                <Link href="/freelancers" className="nav-link">Freelancers</Link>
              </li>
              <li className="nav-item">
                <Link href="/about" className="nav-link">About</Link>
              </li>
            </ul>

            <div className="d-flex align-items-center">
              {user ? (
                <>
                  {!user.is_verified && (
                    <div className="me-3 d-flex align-items-center text-warning">
                      <FiMail className="me-1" />
                      <small>Please verify your email</small>
                    </div>
                  )}

                  <div className="dropdown">
                    <button
                      className="btn btn-link dropdown-toggle d-flex align-items-center text-decoration-none"
                      type="button"
                      data-bs-toggle="dropdown"
                    >
                      <img
                        src={getProfileImageUrl()}
                        alt="Profile"
                        className="rounded-circle me-2"
                        width="32"
                        height="32"
                        style={{ objectFit: 'cover' }}
                      />
                      <span>
                        {user.role === 'freelancer'
                          ? `${user.first_name} ${user.last_name}`
                          : user.company_name
                        }
                      </span>
                    </button>
                    <ul className="dropdown-menu dropdown-menu-end">
                      <li>
                        <Link href="/profile" className="dropdown-item">
                          <FiUser className="me-2" />
                          Profile
                        </Link>
                      </li>
                      <li>
                        <Link href="/settings" className="dropdown-item">
                          <FiSettings className="me-2" />
                          Settings
                        </Link>
                      </li>
                      <li><hr className="dropdown-divider" /></li>
                      <li>
                        <button onClick={logout} className="dropdown-item">
                          <FiLogOut className="me-2" />
                          Logout
                        </button>
                      </li>
                    </ul>
                  </div>
                </>
              ) : (
                <>
                  <Link href="/login" className="btn btn-outline-primary btn-sm me-2">
                    Login
                  </Link>
                  <Link href="/register" className="btn btn-primary btn-sm">
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      <main className="min-vh-100 bg-light">
        {children}
      </main>

      <footer className="bg-dark text-light py-4 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-md-6">
              <h5>DealClosed Partner</h5>
              <p className="mb-0">Connecting companies with top sales professionals.</p>
            </div>
            <div className="col-md-6 text-md-end">
              <p className="mb-0">&copy; 2025 DealClosed Partner. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Layout;
