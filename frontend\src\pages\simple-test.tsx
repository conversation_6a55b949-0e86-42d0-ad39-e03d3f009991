import React from 'react';
import Head from 'next/head';

const SimpleTestPage: React.FC = () => {
  return (
    <>
      <Head>
        <title>Simple Test - DealClosed Partner</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div style={{ 
        minHeight: '100vh', 
        backgroundColor: '#f8f9fa',
        fontFamily: 'Arial, sans-serif'
      }}>
        <nav style={{
          backgroundColor: '#007bff',
          color: 'white',
          padding: '1rem 0'
        }}>
          <div style={{ 
            maxWidth: '1200px', 
            margin: '0 auto', 
            padding: '0 1rem' 
          }}>
            <h1 style={{ margin: 0, fontSize: '1.5rem' }}>
              DealClosed Partner
            </h1>
          </div>
        </nav>

        <div style={{ 
          maxWidth: '800px', 
          margin: '2rem auto', 
          padding: '0 1rem' 
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '2rem',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
          }}>
            <h1 style={{ 
              color: '#007bff', 
              textAlign: 'center',
              marginBottom: '1rem'
            }}>
              🎉 Frontend is Working!
            </h1>
            
            <div style={{
              backgroundColor: '#d4edda',
              border: '1px solid #c3e6cb',
              borderRadius: '4px',
              padding: '1rem',
              marginBottom: '2rem'
            }}>
              <h3 style={{ color: '#155724', margin: '0 0 0.5rem 0' }}>
                ✅ Next.js Setup Complete
              </h3>
              <p style={{ color: '#155724', margin: 0 }}>
                Your frontend is running successfully on Next.js with TypeScript!
              </p>
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <h3>📋 Next Steps:</h3>
              <ol style={{ color: '#666' }}>
                <li>Create PostgreSQL database: <code>CREATE DATABASE sales_platform;</code></li>
                <li>Start the backend server: <code>cd backend && npm run dev</code></li>
                <li>Test the authentication system</li>
              </ol>
            </div>

            <div style={{ 
              display: 'flex', 
              gap: '1rem', 
              justifyContent: 'center',
              flexWrap: 'wrap'
            }}>
              <a 
                href="/test" 
                style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  display: 'inline-block'
                }}
              >
                Full Test Page
              </a>
              <a 
                href="/register" 
                style={{
                  backgroundColor: '#28a745',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  display: 'inline-block'
                }}
              >
                Test Registration
              </a>
              <a 
                href="/login" 
                style={{
                  backgroundColor: '#17a2b8',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  textDecoration: 'none',
                  borderRadius: '4px',
                  display: 'inline-block'
                }}
              >
                Test Login
              </a>
            </div>
          </div>
        </div>

        <footer style={{
          backgroundColor: '#343a40',
          color: 'white',
          textAlign: 'center',
          padding: '2rem 0',
          marginTop: '3rem'
        }}>
          <p style={{ margin: 0 }}>
            &copy; 2025 DealClosed Partner - System Ready!
          </p>
        </footer>
      </div>
    </>
  );
};

export default SimpleTestPage;
