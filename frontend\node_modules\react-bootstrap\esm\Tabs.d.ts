import * as React from 'react';
import PropTypes from 'prop-types';
import { TabsProps as BaseTabsProps } from '@restart/ui/Tabs';
import { NavProps } from './Nav';
import { TransitionType } from './helpers';
export interface TabsProps extends Omit<BaseTabsProps, 'transition'>, Omit<React.HTMLAttributes<HTMLElement>, 'onSelect'>, NavProps {
    transition?: TransitionType;
}
declare const Tabs: {
    (props: TabsProps): JSX.Element;
    propTypes: {
        /**
         * Mark the Tab with a matching `eventKey` as active.
         *
         * @controllable onSelect
         */
        activeKey: PropTypes.Requireable<NonNullable<string | number | null | undefined>>;
        /** The default active key that is selected on start */
        defaultActiveKey: PropTypes.Requireable<NonNullable<string | number | null | undefined>>;
        /**
         * Navigation style
         *
         * @type {('tabs'| 'pills' | 'underline')}
         */
        variant: PropTypes.Requireable<string>;
        /**
         * Sets a default animation strategy for all children `<TabPane>`s.<tbcont
         *
         * Defaults to `<Fade>` animation, else use `false` to disable or a
         * react-transition-group `<Transition/>` component.
         *
         * @type {Transition | false}
         * @default {Fade}
         */
        transition: PropTypes.Requireable<NonNullable<boolean | PropTypes.ReactComponentLike | null | undefined>>;
        /**
         * HTML id attribute, required if no `generateChildId` prop
         * is specified.
         *
         * @type {string}
         */
        id: PropTypes.Requireable<string>;
        /**
         * Callback fired when a Tab is selected.
         *
         * ```js
         * function (
         *   Any eventKey,
         *   SyntheticEvent event?
         * )
         * ```
         *
         * @controllable activeKey
         */
        onSelect: PropTypes.Requireable<(...args: any[]) => any>;
        /**
         * Wait until the first "enter" transition to mount tabs (add them to the DOM)
         */
        mountOnEnter: PropTypes.Requireable<boolean>;
        /**
         * Unmount tabs (remove it from the DOM) when it is no longer visible
         */
        unmountOnExit: PropTypes.Requireable<boolean>;
        /**
         * Have all `Tabs`s proportionately fill all available width.
         */
        fill: PropTypes.Requireable<boolean>;
        /**
         * Have all `Tab`s evenly fill all available width.
         */
        justify: PropTypes.Requireable<boolean>;
    };
    displayName: string;
};
export default Tabs;
