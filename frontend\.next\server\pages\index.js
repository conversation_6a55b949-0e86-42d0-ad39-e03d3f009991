/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=FiBriefcase,FiShield,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js":
/*!************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiBriefcase,FiShield,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiLogOut,FiMail,FiSettings,FiUser!=!./node_modules/react-icons/fi/index.esm.js":
/*!****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiLogOut,FiMail,FiSettings,FiUser!=!./node_modules/react-icons/fi/index.esm.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_FAST_Freelance_Project9_SalesPlatform_DealClosed_frontend_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNpbmRleC50c3gmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUNoQztBQUNMO0FBQzFEO0FBQ29EO0FBQ1Y7QUFDMUM7QUFDb0Q7QUFDcEQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLGlEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLHVCQUF1Qix3RUFBSyxDQUFDLGlEQUFRO0FBQ3JDLHVCQUF1Qix3RUFBSyxDQUFDLGlEQUFRO0FBQ3JDLDJCQUEyQix3RUFBSyxDQUFDLGlEQUFRO0FBQ3pDLGVBQWUsd0VBQUssQ0FBQyxpREFBUTtBQUM3Qix3QkFBd0Isd0VBQUssQ0FBQyxpREFBUTtBQUM3QztBQUNPLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLGlDQUFpQyx3RUFBSyxDQUFDLGlEQUFRO0FBQy9DLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLG9DQUFvQyx3RUFBSyxDQUFDLGlEQUFRO0FBQ3pEO0FBQ08sd0JBQXdCLHlHQUFnQjtBQUMvQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVc7QUFDWCxnQkFBZ0I7QUFDaEIsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELGlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbGNsb3NlZC1mcm9udGVuZC8/OThhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIGFwcCBhbmQgZG9jdW1lbnQgbW9kdWxlcy5cbmltcG9ydCBEb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0IEFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyY1xcXFxwYWdlc1xcXFxpbmRleC50c3hcIjtcbi8vIFJlLWV4cG9ydCB0aGUgY29tcG9uZW50IChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcImdldFN0YXRpY1Byb3BzXCIpO1xuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsIFwiZ2V0U3RhdGljUGF0aHNcIik7XG5leHBvcnQgY29uc3QgZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsIFwiZ2V0U2VydmVyU2lkZVByb3BzXCIpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbmV4cG9ydCBjb25zdCByZXBvcnRXZWJWaXRhbHMgPSBob2lzdCh1c2VybGFuZCwgXCJyZXBvcnRXZWJWaXRhbHNcIik7XG4vLyBSZS1leHBvcnQgbGVnYWN5IG1ldGhvZHMuXG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTdGF0aWNQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFN0YXRpY1BhdGhzXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhcmFtcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFN0YXRpY1BhcmFtc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFNlcnZlclByb3BzXCIpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcInVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wc1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTLFxuICAgICAgICBwYWdlOiBcIi9pbmRleFwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIGNvbXBvbmVudHM6IHtcbiAgICAgICAgQXBwLFxuICAgICAgICBEb2N1bWVudFxuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_FiLogOut_FiMail_FiSettings_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiLogOut,FiMail,FiSettings,FiUser!=!react-icons/fi */ \"__barrel_optimize__?names=FiLogOut,FiMail,FiSettings,FiUser!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__]);\n_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst Layout = ({ children, title = \"DealClosed Partner\", description = \"Connect companies with freelance sales professionals\" })=>{\n    const { user, logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const getProfileImageUrl = ()=>{\n        if (!user?.profile_image || user.profile_image === \"default.png\") {\n            return \"/images/default-avatar.png\";\n        }\n        const folder = user.role === \"company\" ? \"business\" : \"sales\";\n        return `${\"http://localhost:5000\"}/assets/img/${folder}/${user.profile_image}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm border-bottom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/\",\n                            className: \"navbar-brand fw-bold text-primary text-decoration-none\",\n                            children: \"DealClosed Partner\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"navbar-toggler\",\n                            type: \"button\",\n                            \"data-bs-toggle\": \"collapse\",\n                            \"data-bs-target\": \"#navbarNav\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"navbar-toggler-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"collapse navbar-collapse\",\n                            id: \"navbarNav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"navbar-nav me-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/\",\n                                                className: \"nav-link\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/jobs\",\n                                                className: \"nav-link\",\n                                                children: \"Jobs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/freelancers\",\n                                                className: \"nav-link\",\n                                                children: \"Freelancers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/about\",\n                                                className: \"nav-link\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex align-items-center\",\n                                    children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            !user.is_verified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"me-3 d-flex align-items-center text-warning\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_FiMail_FiSettings_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMail, {\n                                                        className: \"me-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        children: \"Please verify your email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"btn btn-link dropdown-toggle d-flex align-items-center text-decoration-none\",\n                                                        type: \"button\",\n                                                        \"data-bs-toggle\": \"dropdown\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: getProfileImageUrl(),\n                                                                alt: \"Profile\",\n                                                                className: \"rounded-circle me-2\",\n                                                                width: \"32\",\n                                                                height: \"32\",\n                                                                style: {\n                                                                    objectFit: \"cover\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 84,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: user.role === \"freelancer\" ? `${user.first_name} ${user.last_name}` : user.company_name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 92,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"dropdown-menu dropdown-menu-end\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/profile\",\n                                                                    className: \"dropdown-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_FiMail_FiSettings_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUser, {\n                                                                            className: \"me-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                            lineNumber: 102,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Profile\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                    lineNumber: 101,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 100,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                    href: \"/settings\",\n                                                                    className: \"dropdown-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_FiMail_FiSettings_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSettings, {\n                                                                            className: \"me-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                            lineNumber: 108,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Settings\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                    className: \"dropdown-divider\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                    lineNumber: 112,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: logout,\n                                                                    className: \"dropdown-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_FiMail_FiSettings_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLogOut, {\n                                                                            className: \"me-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                            lineNumber: 115,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Logout\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                className: \"btn btn-outline-primary btn-sm me-2\",\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                className: \"btn btn-primary btn-sm\",\n                                                children: \"Sign Up\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-vh-100 bg-light\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-dark text-light py-4 mt-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-md-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                        children: \"DealClosed Partner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0\",\n                                        children: \"Connecting companies with top sales professionals.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-md-6 text-md-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-0\",\n                                    children: \"\\xa9 2025 DealClosed Partner. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\components\\\\Layout.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/api */ \"./src/utils/api.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _utils_api__WEBPACK_IMPORTED_MODULE_4__]);\n([js_cookie__WEBPACK_IMPORTED_MODULE_2__, react_hot_toast__WEBPACK_IMPORTED_MODULE_3__, _utils_api__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"token\");\n            if (savedToken) {\n                setToken(savedToken);\n                try {\n                    const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.getMe();\n                    setUser(response.data.data.user);\n                } catch (error) {\n                    // Token is invalid, remove it\n                    js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"token\");\n                    setToken(null);\n                }\n            }\n            setLoading(false);\n        };\n        initAuth();\n    }, []);\n    const login = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.login(data);\n            const { user: userData, token: userToken } = response.data.data;\n            setUser(userData);\n            setToken(userToken);\n            // Save token to cookies\n            const expiresIn = data.remember_me ? 30 : 1; // 30 days or 1 day\n            js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].set(\"token\", userToken, {\n                expires: expiresIn\n            });\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Login successful!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Login failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.register(data);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Registration successful! Please check your email to verify your account.\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Registration failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        setToken(null);\n        js_cookie__WEBPACK_IMPORTED_MODULE_2__[\"default\"].remove(\"token\");\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Logged out successfully\");\n        // Redirect to home page\n        if (false) {}\n    };\n    const updateProfile = async (data)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.userAPI.updateProfile(data);\n            setUser(response.data.data.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Profile updated successfully!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Profile update failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const uploadProfileImage = async (file)=>{\n        try {\n            const formData = new FormData();\n            formData.append(\"profile_image\", file);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.userAPI.uploadImage(formData);\n            // Update user with new profile image\n            if (user) {\n                setUser({\n                    ...user,\n                    profile_image: response.data.data.profile_image\n                });\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Profile image uploaded successfully!\");\n            return response.data;\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || \"Image upload failed\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(errorMessage);\n            throw error.response?.data || {\n                success: false,\n                message: errorMessage\n            };\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.authAPI.getMe();\n            setUser(response.data.data.user);\n        } catch (error) {\n            console.error(\"Failed to refresh user data:\", error);\n        }\n    };\n    const value = {\n        user,\n        token,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        uploadProfileImage,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n([react_hot_toast__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                position: \"top-right\",\n                toastOptions: {\n                    duration: 4000,\n                    style: {\n                        background: \"#363636\",\n                        color: \"#fff\"\n                    },\n                    success: {\n                        duration: 3000,\n                        iconTheme: {\n                            primary: \"#4aed88\",\n                            secondary: \"#fff\"\n                        }\n                    },\n                    error: {\n                        duration: 5000,\n                        iconTheme: {\n                            primary: \"#ff4b4b\",\n                            secondary: \"#fff\"\n                        }\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_FiBriefcase_FiShield_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiBriefcase,FiShield,FiTrendingUp,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiBriefcase,FiShield,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_1__, _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst HomePage = ()=>{\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-primary text-white py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center min-vh-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"display-4 fw-bold mb-4\",\n                                        children: \"Connect Companies with Top Sales Professionals\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"lead mb-4\",\n                                        children: \"DealClosed Partner is the premier platform connecting businesses with skilled freelance sales professionals. Find the perfect match for your sales needs or discover exciting opportunities to grow your career.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"d-flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                className: \"btn btn-light btn-lg\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                className: \"btn btn-outline-light btn-lg\",\n                                                children: \"Sign In\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/dashboard\",\n                                        className: \"btn btn-light btn-lg\",\n                                        children: \"Go to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white bg-opacity-10 rounded-3 p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-3\",\n                                            children: \"Join Our Community\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"row text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"fw-bold\",\n                                                            children: \"500+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 44,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-0\",\n                                                            children: \"Companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"col-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"fw-bold\",\n                                                            children: \"1,200+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 48,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mb-0\",\n                                                            children: \"Sales Professionals\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 49,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row text-center mb-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-3\",\n                                        children: \"Why Choose DealClosed Partner?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"lead text-muted\",\n                                        children: \"We provide the tools and platform you need to succeed in sales partnerships\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row g-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 col-lg-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-100 border-0 shadow-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiShield_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                                    size: 48,\n                                                    className: \"text-primary mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold\",\n                                                    children: \"Verified Professionals\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    children: \"All sales professionals are verified with proper credentials and experience validation.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 col-lg-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-100 border-0 shadow-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiShield_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiBriefcase, {\n                                                    size: 48,\n                                                    className: \"text-primary mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold\",\n                                                    children: \"Quality Opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Access to high-quality sales opportunities from established companies across industries.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 col-lg-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-100 border-0 shadow-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiShield_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTrendingUp, {\n                                                    size: 48,\n                                                    className: \"text-primary mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold\",\n                                                    children: \"Performance Tracking\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Advanced analytics and reporting to track performance and optimize results.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 col-lg-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card h-100 border-0 shadow-sm text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiShield_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShield, {\n                                                    size: 48,\n                                                    className: \"text-primary mb-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold\",\n                                                    children: \"Secure Payments\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    children: \"Secure and timely commission payments with full transparency and tracking.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-light py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-8 mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"fw-bold mb-3\",\n                                    children: \"Ready to Get Started?\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead text-muted mb-4\",\n                                    children: \"Join thousands of companies and sales professionals who trust DealClosed Partner for their sales partnership needs.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                !user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex justify-content-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-primary btn-lg\",\n                                            children: \"Join as Freelancer\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-outline-primary btn-lg\",\n                                            children: \"Post a Job\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/profile\",\n                                    className: \"btn btn-primary btn-lg\",\n                                    children: \"Complete Your Profile\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HomePage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   userAPI: () => (/* binding */ userAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"react-hot-toast\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_2__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, js_cookie__WEBPACK_IMPORTED_MODULE_1__, react_hot_toast__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst API_URL = \"http://localhost:5000\" || 0;\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napi.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    if (error.response?.status === 401) {\n        // Token expired or invalid\n        js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(\"token\");\n        if (false) {}\n    }\n    // Show error toast for non-401 errors\n    if (error.response?.status !== 401) {\n        const message = error.response?.data?.message || \"An error occurred\";\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(message);\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n// API endpoints\nconst authAPI = {\n    register: (data)=>api.post(\"/api/auth/register\", data),\n    login: (data)=>api.post(\"/api/auth/login\", data),\n    logout: ()=>api.post(\"/api/auth/logout\"),\n    getMe: ()=>api.get(\"/api/auth/me\"),\n    verifyEmail: (token)=>api.get(`/api/auth/verify/${token}`)\n};\nconst userAPI = {\n    getProfile: ()=>api.get(\"/api/user/profile\"),\n    updateProfile: (data)=>api.put(\"/api/user/profile\", data),\n    uploadImage: (formData)=>api.post(\"/api/user/upload-image\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        }),\n    deleteAccount: ()=>api.delete(\"/api/user/account\")\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("axios");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "react-hot-toast":
/*!**********************************!*\
  !*** external "react-hot-toast" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-hot-toast");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/bootstrap","vendor-chunks/react-icons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();